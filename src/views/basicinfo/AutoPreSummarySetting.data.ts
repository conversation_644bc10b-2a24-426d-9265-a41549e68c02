import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';

//列表数据
export const columns: BasicColumn[] = [
  {
    title: '启动',
    align: 'center',
    dataIndex: 'enableFlag',
    customRender: ({ text }) => {
      return render.renderSwitch(text, [
        { text: '是', value: '1' },
        { text: '否', value: '0' },
      ]);
    },
  },
  {
    title: '允许未检科室',
    align: 'center',
    dataIndex: 'allowNocheckDepart_dictText',
  },
  {
    title: '允许未检项目',
    align: 'center',
    dataIndex: 'allowNocheckItemgroup_dictText',
  },
  {
    title: '允许体检类别',
    align: 'center',
    dataIndex: 'allowExamCategory_dictText',
  },
];

// 高级查询数据
export const superQuerySchema = {
  enableFlag: { title: '启动', order: 0, view: 'switch', type: 'string' },
  allowNocheckDepart: { title: '允许未检科室', order: 1, view: 'sel_depart', type: 'string' },
  allowNocheckItemgroup: {
    title: '允许未检项目',
    order: 2,
    view: 'sel_search',
    type: 'string',
    dictTable: 'item_group where del_flag=0 and enable_flag=1',
    dictCode: 'id',
    dictText: 'name',
  },
  allowExamCategory: { title: '允许体检类别', order: 3, view: 'list_multi', type: 'string', dictCode: 'examination_type' },
};
