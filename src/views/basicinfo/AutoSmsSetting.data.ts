import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '标题',
    align: 'center',
    dataIndex: 'title',
  },
  {
    title: '业务类别',
    align: 'center',
    dataIndex: 'bizType_dictText',
  },
  /*  {
    title: '体检类别',
    align: 'center',
    dataIndex: 'includeExamCategory_dictText',
  },
  {
    title: '消息模版',
    align: 'center',
    dataIndex: 'msgTemplateId_dictText',
  },*/
  {
    title: '启用',
    align: 'center',
    dataIndex: 'enableFlag',
    customRender: ({ text }) => {
      return render.renderSwitch(text, [
        { text: '是', value: '1' },
        { text: '否', value: '0' },
      ]);
    },
  },
];

// 高级查询数据
export const superQuerySchema = {
  title: { title: '标题', order: 0, view: 'text', type: 'string' },
  bizType: { title: '业务类别', order: 1, view: 'list', type: 'string', dictCode: 'sms_biz_type' },
  includeExamCategory: { title: '体检类别', order: 2, view: 'checkbox', type: 'string', dictCode: 'examination_type' },
  msgTemplateId: {
    title: '消息模版',
    order: 3,
    view: 'sel_search',
    type: 'string',
    dictTable: 'msg_template where enable_flag=1',
    dictCode: 'id',
    dictText: 'title',
  },
  enableFlag: { title: '启用', order: 4, view: 'switch', type: 'string' },
};
