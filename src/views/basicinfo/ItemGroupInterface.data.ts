import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';

//列表数据
export const columns: BasicColumn[] = [
  {
    title: '项目代码',
    align: 'center',
    dataIndex: 'itemCode',
  },
  {
    title: '类别',
    align: 'center',
    dataIndex: 'classCode',
  },
  {
    title: '项目名称',
    align: 'center',
    dataIndex: 'itemName',
    width: 200,
  },
  /*{
    title: '药品规格',
    align: 'center',
    dataIndex: 'itemSpec',
  },
  {
    title: '药品规格单位',
    align: 'center',
    dataIndex: 'itemSpecUnit',
  },*/
  {
    title: '单价',
    align: 'center',
    dataIndex: 'itemPrice',
  },
  {
    title: '单位名称',
    align: 'center',
    dataIndex: 'unitName',
  },
  {
    title: '拼音码',
    align: 'center',
    dataIndex: 'spellCode',
  },
  {
    title: '五笔音首码',
    align: 'center',
    dataIndex: 'wbzxCode',
  },
];

//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '项目代码',
    field: 'itemCode',
    component: 'Input',
    //colProps: {span: 6},
  },
  {
    label: '类别',
    field: 'classCode',
    component: 'Input',
    //colProps: {span: 6},
  },
  {
    label: '项目名称',
    field: 'itemName',
    component: 'Input',
    //colProps: {span: 6},
  },
  {
    label: '拼音码',
    field: 'spellCode',
    component: 'Input',
    //colProps: {span: 6},
  },
];

//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '项目代码',
    field: 'itemCode',
    component: 'Input',
  },
  {
    label: '类别',
    field: 'classCode',
    component: 'Input',
  },
  {
    label: '项目名称',
    field: 'itemName',
    component: 'Input',
  },
  {
    label: '药品规格',
    field: 'itemSpec',
    component: 'Input',
  },
  {
    label: '药品规格单位',
    field: 'itemSpecUnit',
    component: 'Input',
  },
  {
    label: '单价',
    field: 'itemPrice',
    component: 'InputNumber',
  },
  {
    label: '单位名称',
    field: 'unitName',
    component: 'Input',
  },
  {
    label: '拼音码',
    field: 'spellCode',
    component: 'Input',
  },
  {
    label: '五笔音首码',
    field: 'wbzxCode',
    component: 'Input',
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];

// 高级查询数据
export const superQuerySchema = {
  itemCode: { title: '项目代码', order: 0, view: 'text', type: 'string' },
  classCode: { title: '类别', order: 1, view: 'text', type: 'string' },
  itemName: { title: '项目名称', order: 2, view: 'text', type: 'string' },
  itemSpec: { title: '药品规格', order: 3, view: 'text', type: 'string' },
  itemSpecUnit: { title: '药品规格单位', order: 4, view: 'text', type: 'string' },
  itemPrice: { title: '单价', order: 5, view: 'number', type: 'number' },
  unitName: { title: '单位名称', order: 6, view: 'text', type: 'string' },
  spellCode: { title: '拼音码', order: 7, view: 'text', type: 'string' },
  wbzxCode: { title: '五笔音首码', order: 8, view: 'text', type: 'string' },
};
