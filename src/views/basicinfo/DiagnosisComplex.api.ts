import {defHttp} from '/@/utils/http/axios';
import { useMessage } from "/@/hooks/web/useMessage";

const { createConfirm } = useMessage();

enum Api {
  list = '/basicinfo/diagnosisComplex/list',
  save= '/basicinfo/diagnosisComplex/add',
  edit= '/basicinfo/diagnosisComplex/edit',
  deleteOne = '/basicinfo/diagnosisComplex/delete',
  deleteBatch = '/basicinfo/diagnosisComplex/deleteBatch',
  importExcel = '/basicinfo/diagnosisComplex/importExcel',
  exportXls = '/basicinfo/diagnosisComplex/exportXls',
  diagnosisComplexDataList = '/basicinfo/diagnosisComplex/listDiagnosisComplexDataByMainId',
  diagnosisComplexDataSave= '/basicinfo/diagnosisComplex/addDiagnosisComplexData',
  diagnosisComplexDataEdit= '/basicinfo/diagnosisComplex/editDiagnosisComplexData',
  diagnosisComplexDataDelete = '/basicinfo/diagnosisComplex/deleteDiagnosisComplexData',
  diagnosisComplexDataDeleteBatch = '/basicinfo/diagnosisComplex/deleteBatchDiagnosisComplexData',
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;

/**
 * 列表接口
 * @param params
 */
export const list = (params) =>
  defHttp.get({ url: Api.list, params });

/**
 * 删除单个
 */
export const deleteOne = (params,handleSuccess) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
}

/**
 * 批量删除
 * @param params
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    }
  });
}

/**
 * 保存或者更新
 * @param params
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params },{ isTransformResponse: false });
}
  
/**
 * 列表接口
 * @param params
 */
export const diagnosisComplexDataList = (params) => {
  if(params['ruleId']){
    return defHttp.get({ url: Api.diagnosisComplexDataList, params });
  }
  return Promise.resolve({});
}

/**
 * 删除单个
 */
export const diagnosisComplexDataDelete = (params,handleSuccess) => {
  return defHttp.delete({ url: Api.diagnosisComplexDataDelete, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
}

/**
 * 批量删除
 * @param params
 */
export const diagnosisComplexDataDeleteBatch = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.diagnosisComplexDataDeleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    }
  });
}

/**
 * 保存或者更新
 * @param params
 */
export const  diagnosisComplexDataSaveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.diagnosisComplexDataEdit : Api.diagnosisComplexDataSave;
  return defHttp.post({ url: url, params },{ isTransformResponse: false });
}

/**
 * 导入
 */
export const diagnosisComplexDataImportUrl = '/basicinfo/diagnosisComplex/importDiagnosisComplexData'

/**
 * 导出
 */
export const diagnosisComplexDataExportXlsUrl = '/basicinfo/diagnosisComplex/exportDiagnosisComplexData'
