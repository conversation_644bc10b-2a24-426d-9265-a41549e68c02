import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '标题',
    align: 'center',
    dataIndex: 'title',
  },
  {
    title: '类型',
    align: 'center',
    dataIndex: 'type_dictText',
  },
  {
    title: '内容',
    align: 'center',
    dataIndex: 'content',
  },
  {
    title: '附件',
    align: 'center',
    dataIndex: 'attachment',
  },
];

export const columnsLite: BasicColumn[] = [
  {
    title: '标题',
    align: 'center',
    dataIndex: 'title',
  },
  {
    title: '类型',
    align: 'center',
    dataIndex: 'type_dictText',
  },
  {
    title: '附件',
    align: 'center',
    dataIndex: 'attachment',
  },
];

// 高级查询数据
export const superQuerySchema = {
  title: { title: '标题', order: 0, view: 'text', type: 'string' },
  type: { title: '类型', order: 1, view: 'list', type: 'string', dictCode: 'article_type' },
  content: { title: '内容', order: 2, view: 'umeditor', type: 'string' },
  attachment: { title: '附件', order: 3, view: 'file', type: 'string' },
};
