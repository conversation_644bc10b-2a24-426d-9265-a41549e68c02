import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '名称',
    align: 'center',
    dataIndex: 'name',
    width: 150,
  },
  {
    title: '包含大项',
    align: 'center',
    dataIndex: 'groupNames',
    ellipsis: false,
    width: 250,
  },
  {
    title: '条码号来源',
    align: 'center',
    dataIndex: 'barNumSource_dictText',
    width: 100,
  },
  {
    title: '条码号前缀',
    align: 'center',
    dataIndex: 'barNumPrefix',
    width: 100,
  },
  {
    title: '打印内容',
    align: 'center',
    dataIndex: 'text',
    width: 100,
  },
  {
    title: '打印个数',
    align: 'center',
    dataIndex: 'barPage',
    width: 100,
  },
  {
    title: '标本类别',
    align: 'center',
    dataIndex: 'sampleType_dictText',
    width: 100,
  },
  {
    title: '检验方式',
    align: 'center',
    dataIndex: 'testType_dictText',
    width: 100,
  },
  {
    title: '试管颜色',
    align: 'center',
    dataIndex: 'tubeColor_dictText',
    width: 100,
  },
  {
    title: '条码序号',
    align: 'center',
    dataIndex: 'sort',
    width: 100,
  },
  {
    title: '所属位置',
    align: 'center',
    dataIndex: 'printLocation_dictText',
    width: 100,
  },
  {
    title: '启用',
    align: 'center',
    dataIndex: 'enableFlag',
    customRender: ({ text }) => {
      return render.renderSwitch(text, [
        { text: '是', value: 'Y' },
        { text: '否', value: 'N' },
      ]);
    },
  },
];

//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '名称',
    field: 'name',
    component: 'Input',
    //colProps: {span: 6},
  },
  {
    label: '检验方式',
    field: 'testType',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'test_type',
    },
    //colProps: {span: 6},
  },
];

//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '名称',
    field: 'name',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入名称!' }, { ...rules.duplicateCheckRule('barcode_setting', 'name', model, schema)[0] }];
    },
  },
  {
    label: '条码号来源',
    field: 'barNumSource',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'barcode_num_source',
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入条码号来源!' }];
    },
  },
  {
    label: '条码号前缀',
    field: 'barNumPrefix',
    component: 'Input',
  },
  {
    label: '打印内容',
    field: 'text',
    component: 'Input',
  },
  {
    label: '打印个数',
    field: 'barPage',
    component: 'InputNumber',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入打印个数!' }];
    },
  },
  {
    label: '标本类别',
    field: 'sampleType',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'sample_type',
    },
  },
  {
    label: '检验方式',
    field: 'testType',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'test_type',
    },
  },
  {
    label: '试管颜色',
    field: 'tubeColor',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'tube_color',
    },
  },
  {
    label: '条码序号',
    field: 'sort',
    component: 'InputNumber',
  },
  {
    label: '模板',
    field: 'templateId',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'barcode_template,name,id',
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入模板!' }];
    },
  },
  {
    label: '所属位置',
    field: 'printLocation',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'barcode_location',
    },
  },
  {
    label: '启用',
    field: 'enableFlag',
    component: 'JSwitch',
    componentProps: {},
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入启用!' }];
    },
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];

// 高级查询数据
export const superQuerySchema = {
  name: { title: '名称', order: 0, view: 'text', type: 'string' },
  barNumSource: { title: '条码号来源', order: 1, view: 'list', type: 'string', dictCode: 'barcode_num_source' },
  barNumPrefix: { title: '条码号前缀', order: 2, view: 'text', type: 'string' },
  text: { title: '打印内容', order: 3, view: 'text', type: 'string' },
  barPage: { title: '打印个数', order: 4, view: 'number', type: 'number' },
  sampleType: { title: '标本类别', order: 5, view: 'list', type: 'string', dictCode: 'sample_type' },
  testType: { title: '检验方式', order: 6, view: 'list', type: 'string', dictCode: 'test_type' },
  tubeColor: { title: '试管颜色', order: 7, view: 'list', type: 'string', dictCode: 'tube_color' },
  sort: { title: '条码序号', order: 8, view: 'number', type: 'number' },
  printLocation: { title: '所属位置', order: 10, view: 'list', type: 'string', dictCode: 'barcode_location' },
  enableFlag: { title: '启用', order: 11, view: 'number', type: 'number' },
};
