import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '检查部位编码',
    align: "center",
    dataIndex: 'code'
  },
  {
    title: '检查部位名称',
    align: "center",
    dataIndex: 'name'
  },
  {
    title: '拼音缩写',
    align: "center",
    dataIndex: 'helpChar'
  },
  {
    title: '部位分类',
    align: "center",
    dataIndex: 'category'
  },
  {
    title: '排序号',
    align: "center",
    dataIndex: 'sortOrder'
  },
  {
    title: '启用状态',
    align: "center",
    dataIndex: 'enableFlag',
    customRender: ({ text }) => {
      return text === '1' ? '启用' : '禁用';
    }
  },
];

// 高级查询数据
export const superQuerySchema = {
  code: {title: '检查部位编码',order: 0,view: 'text', type: 'string',},
  name: {title: '检查部位名称',order: 1,view: 'text', type: 'string',},
  helpChar: {title: '拼音缩写',order: 2,view: 'text', type: 'string',},
  category: {title: '部位分类',order: 3,view: 'text', type: 'string',},
  enableFlag: {title: '启用状态',order: 4,view: 'list', type: 'string', dictCode: 'yn'},
};

// 表单数据
export const formSchema: FormSchema[] = [
  {
    label: '检查部位编码',
    field: 'code',
    component: 'Input',
    required: true,
  },
  {
    label: '检查部位名称',
    field: 'name',
    component: 'Input',
    required: true,
  },
  {
    label: '拼音缩写',
    field: 'helpChar',
    component: 'Input',
    helpMessage: '用于快速搜索，建议使用拼音首字母',
  },
  {
    label: '部位分类',
    field: 'category',
    component: 'Input',
    helpMessage: '如：关节、脊柱、四肢、躯干等',
  },
  {
    label: '排序号',
    field: 'sortOrder',
    component: 'InputNumber',
    defaultValue: 0,
  },
  {
    label: '启用状态',
    field: 'enableFlag',
    component: 'RadioButtonGroup',
    defaultValue: '1',
    componentProps: {
      options: [
        { label: '启用', value: '1' },
        { label: '禁用', value: '0' },
      ],
    },
  },
  {
    label: '备注',
    field: 'remark',
    component: 'InputTextArea',
  },
];
