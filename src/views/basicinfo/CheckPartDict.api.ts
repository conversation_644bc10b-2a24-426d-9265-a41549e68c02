import { defHttp } from '/@/utils/http/axios';
import { useMessage } from "/@/hooks/web/useMessage";

const { createConfirm } = useMessage();

enum Api {
  list = '/basicinfo/checkPartDict/list',
  save='/basicinfo/checkPartDict/add',
  edit='/basicinfo/checkPartDict/edit',
  deleteOne = '/basicinfo/checkPartDict/delete',
  deleteBatch = '/basicinfo/checkPartDict/deleteBatch',
  importExcel = '/basicinfo/checkPartDict/importExcel',
  exportXls = '/basicinfo/checkPartDict/exportXls',
  listByItemGroup = '/basicinfo/checkPartDict/listByItemGroup',
  searchByKeyword = '/basicinfo/checkPartDict/searchByKeyword',
}

/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;

/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params,handleSuccess) => {
  return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}

/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({url: Api.deleteBatch, data: params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}

/**
 * 保存或者更新
 * @param params
 * @param isUpdate
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params }, { isTransformResponse: false });
}

/**
 * 根据项目ID获取部位选项（带使用频次排序）
 * @param params
 */
export const listByItemGroup = (params) => {
  return defHttp.get({ url: Api.listByItemGroup, params });
}

/**
 * 通用部位搜索接口
 * @param params
 */
export const searchByKeyword = (params) => {
  return defHttp.get({ url: Api.searchByKeyword, params });
}
