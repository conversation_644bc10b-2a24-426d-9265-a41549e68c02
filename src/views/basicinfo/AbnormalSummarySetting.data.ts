import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '标题',
    align: "center",
    dataIndex: 'title'
  },
  {
    title: '排序号',
    align: "center",
    dataIndex: 'seq'
  },
  {
    title: '适配科室',
    align: "center",
    dataIndex: 'departmentId_dictText'
  },
  {
    title: '大项排序号范围',
    align: "center",
    dataIndex: 'itemgroupSortRang'
  },
  {
    title: '适配大项',
    align: "center",
    dataIndex: 'itemgroupIds_dictText'
  },
  {
    title: '汇总内容类型',
    align: "center",
    dataIndex: 'summaryTextType_dictText'
  },
  {
    title: '异常值判断表达式',
    align: "center",
    dataIndex: 'judgeExpression'
  },
  {
    title: '启用标志',
    align: "center",
    dataIndex: 'enableFlag',
    customRender:({text}) => {
       return  render.renderSwitch(text, [{text:'是',value:'1'},{text:'否',value:'0'}]);
     },
  },
];

// 高级查询数据
export const superQuerySchema = {
  title: {title: '标题',order: 0,view: 'text', type: 'string',},
  seq: {title: '排序号',order: 1,view: 'number', type: 'number',},
  departmentId: {title: '适配科室',order: 2,view: 'sel_depart', type: 'string',},
  itemgroupSortRang: {title: '大项排序号范围',order: 3,view: 'text', type: 'string',},
  itemgroupIds: {title: '适配大项',order: 4,view: 'sel_search', type: 'string',dictTable: "item_group where del_flag=0 and enable_flag=1", dictCode: 'id', dictText: 'name',},
  summaryTextType: {title: '汇总内容类型',order: 5,view: 'list', type: 'string',dictCode: 'abnormal_summary_content_type',},
  enableFlag: {title: '启用标志',order: 6,view: 'switch', type: 'string',},
};
