import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '分类名称',
    align: "center",
    dataIndex: 'name'
  },
];

// 高级查询数据
export const superQuerySchema = {
  name: {title: '分类名称',order: 0,view: 'textarea', type: 'string',},
};
