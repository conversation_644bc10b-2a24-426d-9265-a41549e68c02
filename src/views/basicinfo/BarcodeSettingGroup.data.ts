import { BasicColumn, FormSchema } from '/@/components/Table';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '是否有效',
    align: 'center',
    dataIndex: 'validFlag',
  },
  {
    title: '组合名称',
    align: 'center',
    dataIndex: 'groupName',
  },
  {
    title: '组合代码',
    align: 'center',
    dataIndex: 'hisCode',
  },
  {
    title: '简称',
    align: 'center',
    dataIndex: 'shortName',
  },
];

//查询数据
export const searchFormSchema: FormSchema[] = [];

//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '条码设置ID',
    field: 'settingId',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入条码设置ID!' }];
    },
  },
  {
    label: '组合ID',
    field: 'groupId',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入组合ID!' }];
    },
  },
  {
    label: '组合名称',
    field: 'groupName',
    component: 'Input',
  },
  {
    label: '备注',
    field: 'remark',
    component: 'Input',
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];

// 高级查询数据
export const superQuerySchema = {
  settingId: { title: '条码设置ID', order: 0, view: 'text', type: 'string' },
  groupId: { title: '组合ID', order: 1, view: 'text', type: 'string' },
  groupName: { title: '组合名称', order: 2, view: 'text', type: 'string' },
  remark: { title: '备注', order: 3, view: 'text', type: 'string' },
};
