import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '名称',
    align: "center",
    dataIndex: 'name'
  },
  {
    title: '代码',
    align: "center",
    dataIndex: 'code'
  },
  {
    title: '启用标志',
    align: "center",
    dataIndex: 'enableFlag',
    customRender:({text}) => {
       return  render.renderSwitch(text, [{text:'是',value:'Y'},{text:'否',value:'N'}]);
     },
  },
];

// 高级查询数据
export const superQuerySchema = {
  name: {title: '名称',order: 0,view: 'text', type: 'string',},
  code: {title: '代码',order: 1,view: 'text', type: 'string',},
  enableFlag: {title: '启用标志',order: 2,view: 'switch', type: 'string',},
};
