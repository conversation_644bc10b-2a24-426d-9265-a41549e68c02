<template>
  <a-modal v-model:open="visible" title="HIS人员列表" width="80%" @ok="handleOk" @cancel="handleCancel" cancelText="关闭">
    <div style="height: 70vh; overflow-y: auto">
      <!--查询区域-->
      <div class="jeecg-basic-table-form-container">
        <a-form ref="formRef" @keyup.enter="searchQuery" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
          <a-row :gutter="24">
            <a-col :lg="6">
              <a-form-item name="staffName">
                <template #label><span title="员工姓名">员工姓名</span></template>
                <a-input placeholder="请输入员工姓名" v-model:value="queryParam.staffName" />
              </a-form-item>
            </a-col>
            <a-col :lg="6">
              <a-form-item name="adminDeptCode">
                <template #label><span title="行政科室代码">行政科室</span></template>
                <a-input placeholder="请输入行政科室代码" v-model:value="queryParam.adminDeptCode" />
              </a-form-item>
            </a-col>
            <template v-if="toggleSearchStatus">
              <a-col :lg="6">
                <a-form-item name="adminDeptName">
                  <template #label><span title="行政科室名称">行政科室</span></template>
                  <a-input placeholder="请输入行政科室名称" v-model:value="queryParam.adminDeptName" />
                </a-form-item>
              </a-col>
            </template>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
                <a-col :lg="6">
                  <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery">查询</a-button>
                  <a-button type="primary" preIcon="ant-design:reload-outlined" @click="searchReset" style="margin-left: 8px">重置</a-button>
                  <a @click="toggleSearchStatus = !toggleSearchStatus" style="margin-left: 8px">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <Icon :icon="toggleSearchStatus ? 'ant-design:up-outlined' : 'ant-design:down-outlined'" />
                  </a>
                </a-col>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <!--引用表格-->
      <BasicTable @register="registerTable" :rowSelection="rowSelection">
        <template #bodyCell="{ column, record, index, text }"> </template>
      </BasicTable>
    </div>
  </a-modal>
</template>

<script lang="ts" name="basicinfo-sysUserInterface" setup>
  import { reactive, ref } from 'vue';
  import { BasicTable } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { columns } from './SysUserInterface.data';
  import { list } from './SysUserInterface.api';
  import { useUserStore } from '/@/store/modules/user';

  const visible = ref<boolean>(false);
  const emit = defineEmits(['success']);
  const formRef = ref();
  const queryParam = reactive<any>({});
  const toggleSearchStatus = ref<boolean>(false);
  const registerModal = ref();
  const userStore = useUserStore();
  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: 'HIS员工列表',
      api: list,
      columns,
      canResize: false,
      useSearchForm: false,
      showActionColumn: false,
      pagination: {
        pageSize: 50,
      },
      clickToRowSelect: true,
      size: 'small',
      beforeFetch: (params) => {
        return Object.assign(params, queryParam);
      },
    },
  });
  const [
    registerTable,
    { reload, collapseAll, updateTableDataRecord, findTableDataRecord, getDataSource },
    { rowSelection, selectedRowKeys, selectedRows },
  ] = tableContext;
  const labelCol = reactive({
    xs: 24,
    sm: 4,
    xl: 6,
    xxl: 4,
  });
  const wrapperCol = reactive({
    xs: 24,
    sm: 20,
  });

  /**
   * 查询
   */
  function searchQuery() {
    reload({ page: 1 });
  }

  /**
   * 重置
   */
  function searchReset() {
    formRef.value.resetFields();
    selectedRowKeys.value = [];
    //刷新数据
    reload();
  }

  function handleOk() {
    emit('success', selectedRows.value);
    visible.value = false;
  }

  function handleCancel() {
    visible.value = false;
  }

  function open() {
    selectedRowKeys.value = [];
    selectedRows.value = [];
    visible.value = true;
  }

  defineExpose({
    open,
  });
</script>

<style lang="less" scoped>
  .jeecg-basic-table-form-container {
    padding: 0;
    .table-page-search-submitButtons {
      display: block;
      margin-bottom: 24px;
      white-space: nowrap;
    }
    .query-group-cust {
      min-width: 100px !important;
    }
    .query-group-split-cust {
      width: 30px;
      display: inline-block;
      text-align: center;
    }
  }
</style>
