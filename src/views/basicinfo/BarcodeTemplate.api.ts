import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';

const { createConfirm } = useMessage();

enum Api {
  getById = '/basicinfo/barcodeTemplate/getById',
  list = '/basicinfo/barcodeTemplate/list',
  save = '/basicinfo/barcodeTemplate/add',
  edit = '/basicinfo/barcodeTemplate/edit',
  deleteOne = '/basicinfo/barcodeTemplate/delete',
  deleteBatch = '/basicinfo/barcodeTemplate/deleteBatch',
  importExcel = '/basicinfo/barcodeTemplate/importExcel',
  exportXls = '/basicinfo/barcodeTemplate/exportXls',
  getUpdateTime = '/basicinfo/barcodeTemplate/getUpdateTime',
  getBarcodeTemplateIdByCategory = '/basicinfo/barcodeTemplate/getBarcodeTemplateIdByCategory',
}

/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;

/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};

/**
 * 保存或者更新
 * @param params
 * @param isUpdate
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params }, { isTransformResponse: false });
};

export const getBarcodeTemplateById = async (params) => {
  // 检查localStorage中是否已经有了这个ID的数据
  try {
    const cachedData = localStorage.getItem(params.id);
    if (cachedData) {
      const updateTimeOnServerRes = await defHttp.get({ url: Api.getUpdateTime, params }, { isTransformResponse: false });
      if (updateTimeOnServerRes.success) {
        const updateTimeOnServer = updateTimeOnServerRes.result;
        const { data, updateTime } = JSON.parse(cachedData);
        //跟服务器的时间戳进行比较，如果服务器的时间戳大于本地的时间戳，说明数据已经过期
        //将updateTimeOnServer转为时间戳，并判断是否大于本地的时间戳
        if (new Date(updateTimeOnServer).getTime() > new Date(updateTime).getTime()) {
          // 如果数据已经过期，删除localStorage中的数据
          localStorage.removeItem(params.id);
        } else {
          // 如果数据没有过期，直接返回数据
          return data;
        }
      }
    }
  } catch (e) {}

  // 如果localStorage中没有数据，或者数据已经过期，发送请求获取数据
  const data = await defHttp.get({ url: Api.getById, params });

  // 将获取到的数据以及当前的时间戳存入localStorage中
  localStorage.setItem(params.id, JSON.stringify({ data, updateTime: data.updateTime }));

  return data;
};

export const getBarcodeTemplateIdByCategory = (params) =>
  defHttp.get({ url: Api.getBarcodeTemplateIdByCategory, params }, { isTransformResponse: false });
