import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '类别名称',
    align: "center",
    dataIndex: 'categoryName'
  },
  {
    title: '类别描述',
    align: "center",
    dataIndex: 'categoryDesc'
  },
  {
    title: '类别排序',
    align: "center",
    dataIndex: 'categoryOrder'
  },
];

// 高级查询数据
export const superQuerySchema = {
  categoryName: {title: '类别名称',order: 0,view: 'text', type: 'string',},
  categoryDesc: {title: '类别描述',order: 1,view: 'text', type: 'string',},
  categoryOrder: {title: '类别排序',order: 2,view: 'number', type: 'number',},
};
