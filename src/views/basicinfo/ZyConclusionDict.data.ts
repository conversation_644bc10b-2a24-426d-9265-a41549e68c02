import { BasicColumn, FormSchema } from '/@/components/Table';
import { render } from '/@/utils/common/renderUtils';

//列表数据
export const columns: BasicColumn[] = [
  {
    title: '字典分类',
    align: 'center',
    dataIndex: 'dictCate',
  },
  {
    title: '字典值',
    align: 'center',
    dataIndex: 'dictText',
  },
  {
    title: '排序',
    align: 'center',
    dataIndex: 'sort',
  },
  {
    title: '有效',
    align: 'center',
    dataIndex: 'validFlag',
    customRender: ({ text }) => {
      return render.renderSwitch(text, [
        { text: '是', value: '1' },
        { text: '否', value: '0' },
      ]);
    },
  },
  {
    title: '默认',
    align: 'center',
    dataIndex: 'defaultFlag',
    customRender: ({ text }) => {
      return render.renderSwitch(text, [
        { text: '是', value: '1' },
        { text: '否', value: '0' },
      ]);
    },
  },
];

//查询数据
export const searchFormSchema: FormSchema[] = [];

//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '字典分类',
    field: 'dictCate',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: '',
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入字典分类!' }];
    },
  },
  {
    label: '字典值',
    field: 'dictText',
    component: 'InputTextArea',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入字典值!' }];
    },
  },
  {
    label: '排序',
    field: 'sort',
    component: 'InputNumber',
  },
  {
    label: '有效',
    field: 'validFlag',
    component: 'JSwitch',
    componentProps: {
      options: [1, 0],
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入有效!' }];
    },
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];

// 高级查询数据
export const superQuerySchema = {
  dictCate: { title: '字典分类', order: 0, view: 'list', type: 'string' },
  dictText: { title: '字典值', order: 1, view: 'textarea', type: 'string' },
  sort: { title: '排序', order: 2, view: 'number', type: 'number' },
  validFlag: { title: '有效', order: 3, view: 'switch', type: 'string' },
};
