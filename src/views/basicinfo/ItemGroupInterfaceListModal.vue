<template>
  <a-modal v-model:open="visible" title="HIS大项列表" width="80%" @ok="handleOk" @cancel="handleCancel" cancelText="关闭">
    <div style="height: 70vh; overflow-y: auto">
      <!--查询区域-->
      <div class="jeecg-basic-table-form-container">
        <a-form ref="formRef" @keyup.enter="searchQuery" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
          <a-row :gutter="24">
            <a-col :lg="6">
              <a-form-item name="itemCode">
                <template #label><span title="项目代码">项目代码</span></template>
                <a-input placeholder="请输入项目代码" v-model:value="queryParam.itemCode" />
              </a-form-item>
            </a-col>
            <a-col :lg="6">
              <a-form-item name="itemName">
                <template #label><span title="项目名称">项目名称</span></template>
                <a-input placeholder="请输入项目名称" v-model:value="queryParam.itemName" />
              </a-form-item>
            </a-col>

            <template v-if="toggleSearchStatus">
              <a-col :lg="6">
                <a-form-item name="itemPrice">
                  <template #label><span title="价格">价格</span></template>
                  <a-input placeholder="请输入价格" v-model:value="queryParam.itemPrice" />
                </a-form-item>
              </a-col>
              <a-col :lg="6">
                <a-form-item name="classCode">
                  <template #label><span title="类别">类别</span></template>
                  <a-input placeholder="请输入类别" v-model:value="queryParam.classCode" />
                </a-form-item>
              </a-col>
              <a-col :lg="6">
                <a-form-item name="spellCode">
                  <template #label><span title="拼音码">拼音码</span></template>
                  <a-input placeholder="请输入拼音码" v-model:value="queryParam.spellCode" />
                </a-form-item>
              </a-col>
            </template>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
                <a-col :lg="6">
                  <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery">查询</a-button>
                  <a-button type="primary" preIcon="ant-design:reload-outlined" @click="searchReset" style="margin-left: 8px">重置</a-button>
                  <a @click="toggleSearchStatus = !toggleSearchStatus" style="margin-left: 8px">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <Icon :icon="toggleSearchStatus ? 'ant-design:up-outlined' : 'ant-design:down-outlined'" />
                  </a>
                </a-col>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <!--引用表格-->
      <BasicTable @register="registerTable" :rowSelection="rowSelection" />
    </div>
  </a-modal>
</template>

<script lang="ts" name="basicinfo-itemGroupInterface" setup>
  import { reactive, ref } from 'vue';
  import { BasicTable } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { columns } from './ItemGroupInterface.data';
  import { list } from './ItemGroupInterface.api';
  import { useUserStore } from '/@/store/modules/user';

  const visible = ref<boolean>(false);
  const emit = defineEmits(['success']);
  const formRef = ref();
  const queryParam = reactive<any>({});
  const toggleSearchStatus = ref<boolean>(false);
  const registerModal = ref();
  const userStore = useUserStore();
  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: 'HIS大项列表',
      api: list,
      columns,
      canResize: false,
      useSearchForm: false,
      showActionColumn: false,
      pagination: {
        pageSize: 50,
      },
      clickToRowSelect: true,
      size: 'small',
      beforeFetch: (params) => {
        return Object.assign(params, queryParam);
      },
    },
  });
  const [
    registerTable,
    { reload, collapseAll, updateTableDataRecord, findTableDataRecord, getDataSource },
    { rowSelection, selectedRowKeys, selectedRows },
  ] = tableContext;
  const labelCol = reactive({
    xs: 24,
    sm: 4,
    xl: 6,
    xxl: 4,
  });
  const wrapperCol = reactive({
    xs: 24,
    sm: 20,
  });

  /**
   * 查询
   */
  function searchQuery() {
    reload({ page: 1 });
  }

  function handleOk() {
    emit('success', selectedRows.value);
    visible.value = false;
  }

  function handleCancel() {
    visible.value = false;
  }

  function open() {
    selectedRowKeys.value = [];
    selectedRows.value = [];
    visible.value = true;
  }

  /**
   * 重置
   */
  function searchReset() {
    formRef.value.resetFields();
    selectedRowKeys.value = [];
    //刷新数据
    reload();
  }

  defineExpose({
    open,
  });
</script>

<style lang="less" scoped>
  .jeecg-basic-table-form-container {
    padding: 0;

    .table-page-search-submitButtons {
      display: block;
      margin-bottom: 24px;
      white-space: nowrap;
    }

    .query-group-cust {
      min-width: 100px !important;
    }

    .query-group-split-cust {
      width: 30px;
      display: inline-block;
      text-align: center;
    }
  }
</style>
