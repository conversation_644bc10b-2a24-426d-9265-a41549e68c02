import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';

//列表数据
export const columns: BasicColumn[] = [
  /*{
    title: '机构代码',
    align: 'center',
    dataIndex: 'orgCode',
  },
  {
    title: '院区代码',
    align: 'center',
    dataIndex: 'districtCode',
  },*/
  {
    title: '员工代码',
    align: 'center',
    dataIndex: 'id',
  },
  {
    title: '员工工作牌号',
    align: 'center',
    dataIndex: 'employeNo',
  },
  {
    title: '员工姓名',
    align: 'center',
    dataIndex: 'staffName',
  },
  {
    title: '员工性别',
    align: 'center',
    dataIndex: 'sexCode',
  },
  {
    title: '员工出生日期',
    align: 'center',
    dataIndex: 'birthDate',
    customRender: ({ text }) => {
      return !text ? '' : text.length > 10 ? text.substr(0, 10) : text;
    },
  },
  {
    title: '员工身份证号',
    align: 'center',
    dataIndex: 'identityNo',
  },
  {
    title: '任职时间',
    align: 'center',
    dataIndex: 'entryDate',
  },
  {
    title: '行政科室代码',
    align: 'center',
    dataIndex: 'adminDeptCode',
  },
  {
    title: '行政科室名称',
    align: 'center',
    dataIndex: 'adminDeptName',
  },
  {
    title: '行政级别',
    align: 'center',
    dataIndex: 'adminClassCode',
  },
  {
    title: '工作类别编码',
    align: 'center',
    dataIndex: 'workClassCode',
  },
  {
    title: '工作类别名称',
    align: 'center',
    dataIndex: 'workClassName',
  },
  {
    title: '手机号码',
    align: 'center',
    dataIndex: 'mobilePhone',
  },
  {
    title: '在职状态',
    align: 'center',
    dataIndex: 'workStatus',
  },
  {
    title: '职称代码',
    align: 'center',
    dataIndex: 'techTitlesCode',
  },
  {
    title: '职称名称：主任，副主任',
    align: 'center',
    dataIndex: 'techTitlesName',
  },
  {
    title: '员工简介',
    align: 'center',
    dataIndex: 'staffIntroduce',
  },
  {
    title: '员工特长',
    align: 'center',
    dataIndex: 'staffSpeciality',
  },
  {
    title: '行数',
    align: 'center',
    dataIndex: 'rowNumber',
  },
  {
    title: '总数',
    align: 'center',
    dataIndex: 'totalNumber',
  },
];

//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '员工姓名',
    field: 'staffName',
    component: 'Input',
    //colProps: {span: 6},
  },
  {
    label: '行政科室代码',
    field: 'adminDeptCode',
    component: 'Input',
    //colProps: {span: 6},
  },
  {
    label: '行政科室名称',
    field: 'adminDeptName',
    component: 'Input',
    //colProps: {span: 6},
  },
];

//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '机构代码',
    field: 'orgCode',
    component: 'Input',
  },
  {
    label: '院区代码',
    field: 'districtCode',
    component: 'Input',
  },
  {
    label: '员工代码',
    field: 'staffCode',
    component: 'Input',
  },
  {
    label: '员工工作牌号',
    field: 'employeNo',
    component: 'Input',
  },
  {
    label: '员工姓名',
    field: 'staffName',
    component: 'Input',
  },
  {
    label: '员工性别',
    field: 'sexCode',
    component: 'Input',
  },
  {
    label: '员工出生日期',
    field: 'birthDate',
    component: 'DatePicker',
  },
  {
    label: '员工身份证号',
    field: 'identityNo',
    component: 'Input',
  },
  {
    label: '任职时间',
    field: 'entryDate',
    component: 'Input',
  },
  {
    label: '行政科室代码',
    field: 'adminDeptCode',
    component: 'Input',
  },
  {
    label: '行政科室名称',
    field: 'adminDeptName',
    component: 'Input',
  },
  {
    label: '行政级别',
    field: 'adminClassCode',
    component: 'Input',
  },
  {
    label: '工作类别编码',
    field: 'workClassCode',
    component: 'Input',
  },
  {
    label: '工作类别名称',
    field: 'workClassName',
    component: 'Input',
  },
  {
    label: '手机号码',
    field: 'mobilePhone',
    component: 'Input',
  },
  {
    label: '在职状态',
    field: 'workStatus',
    component: 'Input',
  },
  {
    label: '职称代码',
    field: 'techTitlesCode',
    component: 'Input',
  },
  {
    label: '职称名称：主任，副主任',
    field: 'techTitlesName',
    component: 'Input',
  },
  {
    label: '员工简介',
    field: 'staffIntroduce',
    component: 'Input',
  },
  {
    label: '员工特长',
    field: 'staffSpeciality',
    component: 'Input',
  },
  {
    label: '行数',
    field: 'rowNumber',
    component: 'Input',
  },
  {
    label: '总数',
    field: 'totalNumber',
    component: 'Input',
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];

// 高级查询数据
export const superQuerySchema = {
  orgCode: { title: '机构代码', order: 0, view: 'text', type: 'string' },
  districtCode: { title: '院区代码', order: 1, view: 'text', type: 'string' },
  staffCode: { title: '员工代码', order: 2, view: 'text', type: 'string' },
  employeNo: { title: '员工工作牌号', order: 3, view: 'text', type: 'string' },
  staffName: { title: '员工姓名', order: 4, view: 'text', type: 'string' },
  sexCode: { title: '员工性别', order: 5, view: 'text', type: 'string' },
  birthDate: { title: '员工出生日期', order: 6, view: 'date', type: 'string' },
  identityNo: { title: '员工身份证号', order: 7, view: 'text', type: 'string' },
  entryDate: { title: '任职时间', order: 8, view: 'text', type: 'string' },
  adminDeptCode: { title: '行政科室代码', order: 9, view: 'text', type: 'string' },
  adminDeptName: { title: '行政科室名称', order: 10, view: 'text', type: 'string' },
  adminClassCode: { title: '行政级别', order: 11, view: 'text', type: 'string' },
  workClassCode: { title: '工作类别编码', order: 12, view: 'text', type: 'string' },
  workClassName: { title: '工作类别名称', order: 13, view: 'text', type: 'string' },
  mobilePhone: { title: '手机号码', order: 14, view: 'text', type: 'string' },
  workStatus: { title: '在职状态', order: 15, view: 'text', type: 'string' },
  techTitlesCode: { title: '职称代码', order: 16, view: 'text', type: 'string' },
  techTitlesName: { title: '职称名称：主任，副主任', order: 17, view: 'text', type: 'string' },
  staffIntroduce: { title: '员工简介', order: 18, view: 'text', type: 'string' },
  staffSpeciality: { title: '员工特长', order: 19, view: 'text', type: 'string' },
  rowNumber: { title: '行数', order: 20, view: 'text', type: 'string' },
  totalNumber: { title: '总数', order: 21, view: 'text', type: 'string' },
};
