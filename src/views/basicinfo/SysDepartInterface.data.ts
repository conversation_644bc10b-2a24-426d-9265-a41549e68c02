import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '机构代码',
    align: 'center',
    dataIndex: 'orgCode',
  },
  {
    title: '院区代码',
    align: 'center',
    dataIndex: 'districtCode',
  },
  {
    title: '科室代码',
    align: 'center',
    dataIndex: 'deptCode',
  },
  {
    title: '科室名称',
    align: 'center',
    dataIndex: 'deptName',
  },
  {
    title: '科室级别',
    align: 'center',
    dataIndex: 'deptGrade',
  },
  {
    title: '上级科室代码',
    align: 'center',
    dataIndex: 'upperDeptCode',
  },
  {
    title: '上级科室名称',
    align: 'center',
    dataIndex: 'upperDeptName',
  },
  {
    title: '所在病区代码',
    align: 'center',
    dataIndex: 'wardCode',
  },
  {
    title: '所在病区',
    align: 'center',
    dataIndex: 'wardName',
  },
  {
    title: '科室属性',
    align: 'center',
    dataIndex: 'deptAttr',
  },
  {
    title: '核定床位数',
    align: 'center',
    dataIndex: 'checkBedCount',
  },
  {
    title: '有效标志',
    align: 'center',
    dataIndex: 'validFlag',
  },
  {
    title: '拼音码',
    align: 'center',
    dataIndex: 'spellCode',
  },
  {
    title: '五笔音首码',
    align: 'center',
    dataIndex: 'wbzxCode',
  },
  {
    title: '科室类型代码',
    align: 'center',
    dataIndex: 'deptTypeCode',
  },
  {
    title: '科室类型名称',
    align: 'center',
    dataIndex: 'deptTypeName',
  },
  {
    title: '科室特殊标记',
    align: 'center',
    dataIndex: 'specialDeptCode',
  },
  {
    title: '联系电话',
    align: 'center',
    dataIndex: 'deptPhone',
  },
  {
    title: '科室物理地址',
    align: 'center',
    dataIndex: 'deptLocation',
  },
  {
    title: '简称',
    align: 'center',
    dataIndex: 'deptShortName',
  },
  {
    title: '备注',
    align: 'center',
    dataIndex: 'remark',
  },
  {
    title: '科室简介',
    align: 'center',
    dataIndex: 'deptIntro',
  },
  {
    title: '科室别名',
    align: 'center',
    dataIndex: 'deptAlias',
  },
  {
    title: '专业科室代码',
    align: 'center',
    dataIndex: 'professionalDeptCode',
  },
  {
    title: '专业科室名称',
    align: 'center',
    dataIndex: 'professionalDeptName',
  },
  {
    title: '联系电话',
    align: 'center',
    dataIndex: 'tel',
  },
];

//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '科室代码',
    field: 'deptCode',
    component: 'Input',
    //colProps: {span: 6},
  },
  {
    label: '科室名称',
    field: 'deptName',
    component: 'Input',
    //colProps: {span: 6},
  },
];

//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '机构代码',
    field: 'orgCode',
    component: 'Input',
  },
  {
    label: '院区代码',
    field: 'districtCode',
    component: 'Input',
  },
  {
    label: '科室代码',
    field: 'deptCode',
    component: 'Input',
  },
  {
    label: '科室名称',
    field: 'deptName',
    component: 'Input',
  },
  {
    label: '科室级别',
    field: 'deptGrade',
    component: 'Input',
  },
  {
    label: '上级科室代码',
    field: 'upperDeptCode',
    component: 'Input',
  },
  {
    label: '上级科室名称',
    field: 'upperDeptName',
    component: 'Input',
  },
  {
    label: '所在病区代码',
    field: 'wardCode',
    component: 'Input',
  },
  {
    label: '所在病区',
    field: 'wardName',
    component: 'Input',
  },
  {
    label: '科室属性',
    field: 'deptAttr',
    component: 'Input',
  },
  {
    label: '核定床位数',
    field: 'checkBedCount',
    component: 'Input',
  },
  {
    label: '有效标志',
    field: 'validFlag',
    component: 'Input',
  },
  {
    label: '拼音码',
    field: 'spellCode',
    component: 'Input',
  },
  {
    label: '五笔音首码',
    field: 'wbzxCode',
    component: 'Input',
  },
  {
    label: '科室类型代码',
    field: 'deptTypeCode',
    component: 'Input',
  },
  {
    label: '科室类型名称',
    field: 'deptTypeName',
    component: 'Input',
  },
  {
    label: '科室特殊标记',
    field: 'specialDeptCode',
    component: 'Input',
  },
  {
    label: '联系电话',
    field: 'deptPhone',
    component: 'Input',
  },
  {
    label: '科室物理地址',
    field: 'deptLocation',
    component: 'Input',
  },
  {
    label: '简称',
    field: 'deptShortName',
    component: 'Input',
  },
  {
    label: '备注',
    field: 'remark',
    component: 'Input',
  },
  {
    label: '科室简介',
    field: 'deptIntro',
    component: 'Input',
  },
  {
    label: '科室别名',
    field: 'deptAlias',
    component: 'Input',
  },
  {
    label: '专业科室代码',
    field: 'professionalDeptCode',
    component: 'Input',
  },
  {
    label: '专业科室名称',
    field: 'professionalDeptName',
    component: 'Input',
  },
  {
    label: '联系电话',
    field: 'tel',
    component: 'Input',
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];

// 高级查询数据
export const superQuerySchema = {
  orgCode: { title: '机构代码', order: 0, view: 'text', type: 'string' },
  districtCode: { title: '院区代码', order: 1, view: 'text', type: 'string' },
  deptCode: { title: '科室代码', order: 2, view: 'text', type: 'string' },
  deptName: { title: '科室名称', order: 3, view: 'text', type: 'string' },
  deptGrade: { title: '科室级别', order: 4, view: 'text', type: 'string' },
  upperDeptCode: { title: '上级科室代码', order: 5, view: 'text', type: 'string' },
  upperDeptName: { title: '上级科室名称', order: 6, view: 'text', type: 'string' },
  wardCode: { title: '所在病区代码', order: 7, view: 'text', type: 'string' },
  wardName: { title: '所在病区', order: 8, view: 'text', type: 'string' },
  deptAttr: { title: '科室属性', order: 9, view: 'text', type: 'string' },
  checkBedCount: { title: '核定床位数', order: 10, view: 'text', type: 'string' },
  validFlag: { title: '有效标志', order: 11, view: 'text', type: 'string' },
  spellCode: { title: '拼音码', order: 12, view: 'text', type: 'string' },
  wbzxCode: { title: '五笔音首码', order: 13, view: 'text', type: 'string' },
  deptTypeCode: { title: '科室类型代码', order: 14, view: 'text', type: 'string' },
  deptTypeName: { title: '科室类型名称', order: 15, view: 'text', type: 'string' },
  specialDeptCode: { title: '科室特殊标记', order: 16, view: 'text', type: 'string' },
  deptPhone: { title: '联系电话', order: 17, view: 'text', type: 'string' },
  deptLocation: { title: '科室物理地址', order: 18, view: 'text', type: 'string' },
  deptShortName: { title: '简称', order: 19, view: 'text', type: 'string' },
  remark: { title: '备注', order: 20, view: 'text', type: 'string' },
  deptIntro: { title: '科室简介', order: 21, view: 'text', type: 'string' },
  deptAlias: { title: '科室别名', order: 22, view: 'text', type: 'string' },
  professionalDeptCode: { title: '专业科室代码', order: 23, view: 'text', type: 'string' },
  professionalDeptName: { title: '专业科室名称', order: 24, view: 'text', type: 'string' },
  tel: { title: '联系电话', order: 25, view: 'text', type: 'string' },
};
