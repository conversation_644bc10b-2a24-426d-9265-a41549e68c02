<template>
  <a-row :gutter="8">
    <a-col :span="10">
      <div class="p-2">
        <!--查询区域-->
        <div class="jeecg-basic-table-form-container">
          <a-form ref="formRef" @keyup.enter="reload" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
            <a-row :gutter="24">
              <a-col :sm="12">
                <a-form-item name="departmentId">
                  <template #label><span title="科室ID">科室ID</span></template>
                  <j-select-dept placeholder="请选择科室ID" v-model:value="queryParam.departmentId" checkStrictly />
                </a-form-item>
              </a-col>
              <template v-if="toggleSearchStatus">
                <a-col :sm="8">
                  <a-form-item name="enableFlag">
                    <template #label><span title="启用">启用</span></template>
                    <j-switch placeholder="请选择启用" v-model:value="queryParam.enableFlag" :options="[1, 0]" query />
                  </a-form-item>
                </a-col>
              </template>
              <a-col :xl="6" :lg="7" :md="8" :sm="24">
                <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
                  <a-col :lg="6">
                    <a-button type="primary" preIcon="ant-design:search-outlined" @click="reload">查询</a-button>
                    <a-button preIcon="ant-design:reload-outlined" @click="searchReset" style="margin-left: 8px">重置</a-button>
                    <a @click="toggleSearchStatus = !toggleSearchStatus" style="margin-left: 8px">
                      {{ toggleSearchStatus ? '收起' : '展开' }}
                      <Icon :icon="toggleSearchStatus ? 'ant-design:up-outlined' : 'ant-design:down-outlined'" />
                    </a>
                  </a-col>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <!--引用表格-->
        <BasicTable @register="registerTable" :rowSelection="rowSelection">
          <!--插槽:table标题-->
          <template #tableTitle>
            <a-button type="primary" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
            <!--            <a-button type="primary" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button>
            <j-upload-button type="primary" preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button>-->
            <a-dropdown v-if="selectedRowKeys.length > 0">
              <template #overlay>
                <a-menu>
                  <a-menu-item key="1" @click="batchHandleDelete">
                    <Icon icon="ant-design:delete-outlined" />
                    删除
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button
                >批量操作
                <Icon icon="mdi:chevron-down" />
              </a-button>
            </a-dropdown>
            <!-- 高级查询 -->
            <super-query :config="superQueryConfig" @search="handleSuperQuery" />
          </template>
          <!--操作栏-->
          <template #action="{ record }">
            <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
          </template>
          <!--字段回显插槽-->
          <template #bodyCell="{ column, record, index, text }"> </template>
        </BasicTable>
      </div>
    </a-col>
    <a-col :span="14">
      <div class="p-2">
        <a-card title="详情">
          <DiagnosisComplexForm ref="registerForm" @ok="submitCallback" :formDisabled="false" :formBpm="false" />
          <a-flex justify="right"> <a-button type="primary" @click="save">保存</a-button></a-flex>
        </a-card>
      </div>
    </a-col>
  </a-row>
</template>

<script lang="ts" name="basicinfo-diagnosisComplex" setup>
  import { ref, reactive, computed, unref, provide } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { columns, searchFormSchema, superQuerySchema } from './DiagnosisComplex.data';
  import { list, deleteOne, batchDelete, getImportUrl, getExportUrl } from './DiagnosisComplex.api';
  import { downloadFile } from '/@/utils/common/renderUtils';
  import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import JSwitch from '/@/components/Form/src/jeecg/components/JSwitch.vue';
  import JSelectDept from '/@/components/Form/src/jeecg/components/JSelectDept.vue';
  import { useUserStore } from '/@/store/modules/user';
  import DiagnosisComplexForm from '@/views/basicinfo/components/DiagnosisComplexForm.vue';
  import { theme } from 'ant-design-vue';

  const formRef = ref();
  const queryParam = reactive<any>({});
  const checkedKeys = ref<Array<string | number>>([]);
  const registerModal = ref();
  const userStore = useUserStore();
  const registerForm = ref();
  const { token } = theme.useToken();

  const currentRow = ref();
  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: '符合判断',
      api: list,
      columns,
      canResize: false,
      useSearchForm: false,
      actionColumn: {
        width: 120,
        fixed: 'right',
      },
      size: 'small',
      pagination: { pageSize: 15 },
      beforeFetch: (params) => {
        return Object.assign(params, queryParam);
      },
      rowClassName: (record) => {
        return currentRow.value && currentRow.value.id === record.id ? 'row-selected' : '';
      },
    },
    exportConfig: {
      name: '符合判断',
      url: getExportUrl,
      params: queryParam,
    },
    importConfig: {
      url: getImportUrl,
      success: handleSuccess,
    },
  });

  const [registerTable, { reload }, { rowSelection, selectedRowKeys }] = tableContext;
  const mainId = computed(() => (unref(selectedRowKeys).length > 0 ? unref(selectedRowKeys)[0] : ''));
  //下发 mainId,子组件接收
  provide('mainId', mainId);

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    reload();
  }

  /**
   * 新增事件
   */
  function handleAdd() {
    currentRow.value = {};
    registerForm.value.add();
  }

  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    currentRow.value = record;
    registerForm.value.edit(record);
  }

  function save() {
    registerForm.value.submitForm();
  }
  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteOne({ id: record.id }, handleSuccess);
  }

  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    await batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
  }

  /**
   * 成功回调
   */
  function handleSuccess() {
    if (currentRow.value && selectedRowKeys.value.includes(currentRow.value.id)) {
      currentRow.value = null;
      registerForm.value.resetFields();
    }
    (selectedRowKeys.value = []) && reload();
  }

  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
      },
    ];
  }

  /**
   * 下拉操作栏
   */
  function getDropDownAction(record) {
    return [
      /*{
        label: '详情',
        onClick: handleDetail.bind(null, record),
      },
      {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
          placement: 'topLeft',
        },
      },*/
    ];
  }

  /**
   * form保存回调事件
   */
  function submitCallback() {
    reload();
  }

  /* ----------------------以下为原生查询需要添加的-------------------------- */
  const toggleSearchStatus = ref<boolean>(false);
  const labelCol = reactive({
    xs: 24,
    sm: 4,
    xl: 6,
    xxl: 4,
  });
  const wrapperCol = reactive({
    xs: 24,
    sm: 20,
  });

  /**
   * 重置
   */
  function searchReset() {
    formRef.value.resetFields();
    selectedRowKeys.value = [];
    //刷新数据
    reload();
  }
</script>
<style lang="less" scoped>
  .jeecg-basic-table-form-container {
    padding: 0;
    .table-page-search-submitButtons {
      display: block;
      margin-bottom: 12px;
      white-space: nowrap;
    }
    .query-group-cust {
      min-width: 100px !important;
    }
    .query-group-split-cust {
      width: 30px;
      display: inline-block;
      text-align: center;
    }
  }

  :deep(.row-selected td:first-child) {
    border-left: solid 5px v-bind('token.colorPrimary');
  }
</style>
