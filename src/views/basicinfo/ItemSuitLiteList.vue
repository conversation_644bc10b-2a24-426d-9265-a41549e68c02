<template>
  <div class="p-2">
    <!-- 显示模式切换 -->
    <div style="margin-bottom: 8px; display: flex; justify-content: space-between; align-items: center;">
      <a-radio-group v-model:value="displayMode" @change="toggleDisplayMode">
        <a-radio-button value="tree">分类树</a-radio-button>
        <a-radio-button value="list">列表</a-radio-button>
      </a-radio-group>

      <!-- 搜索框 -->
      <div style="width: 300px;">
        <a-input
          v-model:value="searchKeyword"
          placeholder="搜索套餐名称、编码..."
          allowClear
          :disabled="props.disabled"

        >
          <template #prefix>
            <Icon icon="ant-design:search-outlined" />
          </template>
        </a-input>
      </div>
    </div>

    <!-- 分类树模式 -->
    <div v-if="displayMode === 'tree'" class="suit-category-tree">
      <a-spin :spinning="treeLoading">
        <div v-if="filteredCategories.length === 0 && !treeLoading" class="empty-state">
          <a-empty description="暂无套餐分类数据" />
        </div>

        <div v-else class="tree-container">
          <div
            v-for="category in filteredCategories"
            :key="category.id"
            class="category-group"
          >
            <!-- 分类节点 -->
            <div
              class="category-node"
              :class="{ expanded: category.isExpanded, disabled: props.disabled }"
              @click="!props.disabled && toggleCategoryExpansion(category.id)"
            >
              <div class="category-content">
                <Icon
                  :icon="category.isLoading ? 'ant-design:loading-outlined' :
                         category.isExpanded ? 'ant-design:folder-open-outlined' : 'ant-design:folder-outlined'"
                  :spin="category.isLoading"
                  class="category-icon"
                />
                <span class="category-name">{{ category.name }}</span>
                <span class="category-count">({{ category.children.length }})</span>
              </div>
            </div>

            <!-- 套餐子节点 -->
            <div v-if="category.isExpanded" class="suits-container">
              <div
                v-for="suit in category.children"
                :key="suit.id"
                class="suit-node"
                :class="{
                  selected: suit.isSelected,
                  disabled: !isSuitEnabled(suit) || props.disabled
                }"
                @click="!props.disabled && isSuitEnabled(suit) && selectSuit(suit)"
              >
                <div class="suit-content">
                  <Icon icon="ant-design:gift-outlined" class="suit-icon" />
                  <div class="suit-info">
                    <div class="suit-name">{{ suit.name }}</div>
                    <div class="suit-meta">
                      <span v-if="suit.code" class="suit-code">{{ suit.code }}</span>
                      <span v-if="suit.price" class="suit-price">¥{{ suit.price }}</span>
                    </div>
                  </div>
                  <div class="suit-actions">
                    <a-button
                      type="link"
                      size="small"
                      :disabled="props.disabled || !isSuitEnabled(suit)"
                      @click.stop="handleUse(suit)"
                    >
                      使用
                    </a-button>
                    <a-button type="link" size="small" @click.stop="handleDetail(suit)">详情</a-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </a-spin>
    </div>

    <!-- 列表模式（原有功能） -->
    <div v-else>
      <!--查询区域-->
      <div class="jeecg-basic-table-form-container" v-if="toggleSearchStatus">
        <a-form ref="formRef" @keyup.enter="searchQuery" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
          <a-row :gutter="24">
            <a-col :lg="8" md="8">
              <a-form-item name="helpChar">
                <template #label><span title="助记码">助记码</span></template>
                <a-input placeholder="请输入助记码" v-model:value="queryParam.helpChar" />
              </a-form-item>
            </a-col>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
                <a-col :lg="6">
                  <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery">查询</a-button>
                  <a-button type="primary" preIcon="ant-design:reload-outlined" @click="searchReset" style="margin-left: 8px">重置</a-button>
                  <a @click="toggleSearchStatus = !toggleSearchStatus" style="margin-left: 8px">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <Icon :icon="toggleSearchStatus ? 'ant-design:up-outlined' : 'ant-design:down-outlined'" />
                  </a>
                </a-col>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <!--引用表格-->
      <BasicTable @register="registerTable" :rowSelection="null">
        <!--操作栏-->
        <template #action="{ record }">
          <TableAction :actions="getTableAction(record)" />
        </template>
        <template #bodyCell="{ column, record, index, text }"> </template>
      </BasicTable>
    </div>

    <!-- 表单区域 -->
    <SuitDetail ref="registerDetail" />
  </div>
</template>

<script lang="ts" name="basicinfo-itemSuit" setup>
  import { ref, reactive, computed, onMounted, watch } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { columnsLite, superQuerySchema } from './ItemSuit.data';
  import { list, listSuitByKeyword } from './ItemSuit.api';
  import { list as listSuitCategories } from './SuitCategory.api';
  import { useUserStore } from '/@/store/modules/user';
  import SuitDetail from '@/views/basicinfo/components/SuitDetail.vue';
  import { debounce } from 'lodash-es';
  import { Icon } from '@/components/Icon';

  // 套餐分类树相关类型定义
  interface SuitCategoryNode {
    id: string;
    name: string;
    code?: string;
    description?: string;
    sort?: number;
    isExpanded: boolean;
    isLoading: boolean;
    children: SuitNode[];
    childrenLoaded: boolean;
    nodeType: 'category';
  }

  interface SuitNode {
    id: string;
    name: string;
    code?: string;
    description?: string;
    price?: number;
    categoryId: string;
    categoryName: string;
    enableFlag: string;
    sort?: number;
    isSelected: boolean;
    nodeType: 'suit';
  }

  const props = defineProps({
    disabled: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(['use']);
  const formRef = ref();
  const queryParam = reactive<any>({});
  queryParam.enableFlag = 1;
  const toggleSearchStatus = ref<boolean>(false);
  const userStore = useUserStore();
  const registerDetail = ref();

  // 套餐分类树相关数据
  const suitCategories = ref<SuitCategoryNode[]>([]);
  const filteredCategories = ref<SuitCategoryNode[]>([]);
  const searchKeyword = ref('');
  const treeLoading = ref(false);
  const expandedKeys = ref(new Set<string>());
  const selectedSuit = ref<SuitNode | null>(null);
  const displayMode = ref<'tree' | 'list'>('tree'); // 显示模式：树形或列表
  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: '体检套餐',
      size: 'small',
      api: list,
      columns: columnsLite,
      showIndexColumn: true,
      canResize: false,
      useSearchForm: false,
      clickToRowSelect: false,
      actionColumn: {
        width: 120,
        fixed: 'right',
      },
      beforeFetch: (params) => {
        return Object.assign(params, queryParam);
      },
    },
  });
  const [registerTable, { reload, collapseAll, updateTableDataRecord, findTableDataRecord, getDataSource }, { rowSelection, selectedRowKeys }] =
    tableContext;
  const labelCol = reactive({
    xs: 24,
    md: 8,
    sm: 6,
    lg: 6,
    xl: 6,
    xxl: 4,
  });
  const wrapperCol = reactive({
    xs: 24,
    sm: 18,
    md: 16,
    lg: 18,
  });

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 详情
   */
  function handleDetail(record: Recordable | SuitNode) {
    // 如果是 SuitNode 类型，转换为 Recordable 格式
    if ('nodeType' in record && record.nodeType === 'suit') {
      const recordData = {
        id: record.id,
        name: record.name,
        code: record.code,
        price: record.price,
        categoryId: record.categoryId,
        categoryName: record.categoryName,
        enableFlag: record.enableFlag,
      };
      registerDetail.value.showDetail(recordData);
    } else {
      registerDetail.value.showDetail(record);
    }
  }

  /**
   * 使用
   */
  function handleUse(record: Recordable | SuitNode) {
    if (props.disabled) {
      return;
    }

    // 如果是 SuitNode 类型，检查启用状态
    if ('nodeType' in record && record.nodeType === 'suit') {
      if (!isSuitEnabled(record)) {
        return;
      }
    }

    emit('use', record);
  }

  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }

  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '使用',
        onClick: handleUse.bind(null, record),
      },
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      },
    ];
  }

  /**
   * 查询
   */
  function searchQuery() {
    reload({ page: 1 });
  }

  /**
   * 重置
   */
  function searchReset() {
    formRef.value.resetFields();
    selectedRowKeys.value = [];
    searchKeyword.value = '';
    //刷新数据
    if (displayMode.value === 'list') {
      reload();
    } else {
      filteredCategories.value = [...suitCategories.value];
    }
  }

  // 套餐分类树相关方法
  async function loadSuitCategories() {
    try {
      treeLoading.value = true;
      console.log('开始加载套餐分类...');

      const response = await listSuitCategories({ pageSize: 1000 });
      console.log('套餐分类API响应:', response);

      if (response && response.records && Array.isArray(response.records)) {
        // 直接使用响应数据，因为这个API返回的就是分页数据格式
        const categories = response.records.map(category => ({
          id: category.id,
          name: category.categoryName,
          code: category.categoryCode,
          description: category.categoryDesc,
          sort: category.categoryOrder || 0,
          isExpanded: false,
          isLoading: false,
          children: [],
          childrenLoaded: false,
          nodeType: 'category' as const,
        }));

        categories.sort((a, b) => (a.sort || 0) - (b.sort || 0));
        suitCategories.value = categories;
        filteredCategories.value = [...categories];

        console.log('成功加载套餐分类:', categories.length, '个');
        return categories;
      } else {
        console.error('获取套餐分类失败: 响应数据格式异常', response);
        return [];
      }
    } catch (error) {
      console.error('加载套餐分类异常:', error);
      console.error('错误详情:', error.message, error.stack);
      return [];
    } finally {
      treeLoading.value = false;
    }
  }

  async function loadSuitsByCategory(categoryId: string): Promise<SuitNode[]> {
    try {
      console.log('开始加载分类套餐, categoryId:', categoryId);

      const response = await list({
        categoryId: categoryId,
        pageSize: 1000,
        enableFlag: '1'
      });

      console.log('套餐API响应:', response);

      // 检查不同的响应格式
      let records = [];
      if (response && response.success && response.result?.records) {
        records = response.result.records;
      } else if (response && response.records) {
        records = response.records;
      } else if (response && Array.isArray(response)) {
        records = response;
      }

      if (!Array.isArray(records)) {
        console.warn('套餐数据格式异常:', response);
        return [];
      }

      const suits = records.map(suit => {
        console.log('套餐数据:', suit);
        console.log('enableFlag值:', suit.enableFlag, '类型:', typeof suit.enableFlag);

        return {
          id: suit.id,
          name: suit.name,
          code: suit.code,
          description: suit.description,
          price: suit.price,
          categoryId: suit.categoryId,
          categoryName: suit.categoryName,
          enableFlag: suit.enableFlag,
          sort: suit.sort || 0,
          isSelected: false,
          nodeType: 'suit' as const,
        };
      });

      suits.sort((a, b) => (a.sort || 0) - (b.sort || 0));
      console.log('成功加载套餐:', suits.length, '个');
      return suits;
    } catch (error) {
      console.error('加载套餐列表异常:', error);
      return [];
    }
  }

  async function toggleCategoryExpansion(categoryId: string) {
    const category = suitCategories.value.find(cat => cat.id === categoryId);
    if (!category) return;

    if (category.isExpanded) {
      category.isExpanded = false;
      expandedKeys.value.delete(categoryId);
    } else {
      if (!category.childrenLoaded) {
        category.isLoading = true;
        try {
          const suits = await loadSuitsByCategory(categoryId);
          category.children = suits;
          category.childrenLoaded = true;
        } finally {
          category.isLoading = false;
        }
      }
      category.isExpanded = true;
      expandedKeys.value.add(categoryId);
    }

    // 更新过滤后的分类
    filteredCategories.value = [...suitCategories.value];
  }

  // 判断套餐是否启用
  function isSuitEnabled(suit: SuitNode): boolean {
    // 支持多种启用标识格式
    const flag = suit.enableFlag;

    // 字符串 '1' 或 'true'
    if (typeof flag === 'string') {
      return flag === '1' || flag.toLowerCase() === 'true';
    }

    // 数字 1
    if (typeof flag === 'number') {
      return flag === 1;
    }

    // 布尔值
    if (typeof flag === 'boolean') {
      return flag;
    }

    // 默认启用（如果字段不存在或为其他值）
    console.warn('未知的enableFlag格式:', flag, '套餐:', suit.name);
    return true;
  }

  function selectSuit(suit: SuitNode) {
    selectedSuit.value = suit;
    // 清除其他套餐的选中状态
    suitCategories.value.forEach(category => {
      category.children.forEach(s => {
        s.isSelected = s.id === suit.id;
      });
    });
  }

  // 搜索功能
  const handleSearch = debounce(async (keyword: string) => {
    // 确保keyword是字符串
    const searchTerm = String(keyword || '');

    if (!searchTerm.trim()) {
      filteredCategories.value = [...suitCategories.value];
      return;
    }

    if (displayMode.value === 'list') {
      // 列表模式使用原有的搜索逻辑
      queryParam.name = searchTerm;
      reload({ page: 1 });
    } else {
      // 树形模式搜索
      try {
        const searchResults = await listSuitByKeyword({
          keyword: searchTerm,
          pageSize: 100,
          enableFlag: '1'
        });

        // 处理不同的搜索结果格式
        let searchData = [];
        if (searchResults && searchResults.success && searchResults.result) {
          searchData = Array.isArray(searchResults.result) ? searchResults.result : searchResults.result.records || [];
        } else if (searchResults && Array.isArray(searchResults)) {
          searchData = searchResults;
        }

        if (searchData.length > 0) {
          // 将搜索结果按分类组织
          const categoryMap = new Map<string, SuitNode[]>();

          for (const suit of searchData) {
            const suitNode: SuitNode = {
              id: suit.id,
              name: suit.name,
              code: suit.code,
              description: suit.description,
              price: suit.price,
              categoryId: suit.categoryId,
              categoryName: suit.categoryName,
              enableFlag: suit.enableFlag,
              sort: suit.sort || 0,
              isSelected: false,
              nodeType: 'suit',
            };

            if (!categoryMap.has(suit.categoryId)) {
              categoryMap.set(suit.categoryId, []);
            }
            categoryMap.get(suit.categoryId)!.push(suitNode);
          }

          // 构建过滤后的分类树
          const filtered: SuitCategoryNode[] = [];
          for (const category of suitCategories.value) {
            const suits = categoryMap.get(category.id) || [];
            if (suits.length > 0) {
              filtered.push({
                ...category,
                children: suits,
                isExpanded: true,
                childrenLoaded: true,
              });
            }
          }

          filteredCategories.value = filtered;
        }
      } catch (error) {
        console.error('搜索套餐失败:', error);
      }
    }
  }, 300);

  // 切换显示模式
  function toggleDisplayMode() {
    console.log('切换显示模式到:', displayMode.value);

    if (displayMode.value === 'tree' && suitCategories.value.length === 0) {
      loadSuitCategories();
    }
  }

  // 套餐操作方法（handleUse 已在上面定义，这里不需要重复）



  // 监听搜索关键字变化
  watch(searchKeyword, (newKeyword) => {
    handleSearch(newKeyword);
  });

  // 组件初始化
  onMounted(() => {
    if (displayMode.value === 'tree') {
      loadSuitCategories();
    }
  });
</script>

<style lang="less" scoped>
  .jeecg-basic-table-form-container {
    padding: 0;
    .table-page-search-submitButtons {
      display: block;
      margin-bottom: 12px;
      white-space: nowrap;
    }
    .query-group-cust {
      min-width: 100px !important;
    }
    .query-group-split-cust {
      width: 30px;
      display: inline-block;
      text-align: center;
    }
  }

  /* 套餐分类树样式 */
  .suit-category-tree {
    .empty-state {
      text-align: center;
      padding: 40px 20px;
      color: #999;
    }

    .category-group {
      margin-bottom: 3px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .tree-container {
      max-height: 75vh;
      overflow-y: auto;
      padding: 2px;

      /* 优化滚动条样式 */
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;

        &:hover {
          background: #a8a8a8;
        }
      }
    }

    .category-node {
      padding: 6px 12px;
      cursor: pointer;
      border-radius: 4px;
      transition: all 0.15s;
      user-select: none;
      margin-bottom: 2px;
      border: 1px solid transparent;
      background: #fafafa;

      &:hover {
        background-color: #f0f9ff;
        border-color: #d6e4ff;
      }

      &.expanded {
        background-color: #e6f7ff;
        border-color: #91d5ff;
      }

      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;

        &:hover {
          background-color: #fafafa;
          border-color: transparent;
        }
      }

      .category-content {
        display: flex;
        align-items: center;
        gap: 6px;

        .category-icon {
          font-size: 14px;
          color: #1890ff;
          flex-shrink: 0;
        }

        .category-name {
          font-weight: 600;
          font-size: 13px;
          color: #1890ff;
          flex: 1;
          line-height: 1.2;
        }

        .category-count {
          font-size: 11px;
          color: #666;
          background: #e6f7ff;
          padding: 1px 5px;
          border-radius: 8px;
          min-width: 16px;
          text-align: center;
          line-height: 1.3;
        }
      }
    }

    .suits-container {
      margin-left: 16px;
      margin-top: 2px;
      border-left: 1px solid #d6e4ff;
      padding-left: 8px;

      .suit-node {
        padding: 4px 8px;
        cursor: pointer;
        border-radius: 3px;
        transition: all 0.15s;
        margin-bottom: 1px;
        border: 1px solid transparent;
        background: #fdfdfd;

        &:hover {
          background-color: #f5f5f5;
          border-color: #d9d9d9;
        }

        &.selected {
          background-color: #e6f7ff;
          border-color: #91d5ff;
        }

        &.disabled {
          opacity: 0.4;
          cursor: not-allowed;

          &:hover {
            background-color: #fdfdfd;
            border-color: transparent;
          }
        }

        .suit-content {
          display: flex;
          align-items: center;
          gap: 6px;

          .suit-icon {
            font-size: 12px;
            color: #52c41a;
            flex-shrink: 0;
          }

          .suit-info {
            flex: 1;
            min-width: 0;

            .suit-name {
              font-size: 12px;
              color: #333;
              font-weight: 500;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              line-height: 1.3;
              margin-bottom: 1px;
            }

            .suit-meta {
              display: flex;
              align-items: center;
              gap: 6px;
              line-height: 1.2;

              .suit-code {
                font-size: 10px;
                color: #999;
                background: #f0f0f0;
                padding: 0px 3px;
                border-radius: 2px;
                line-height: 1.4;
              }

              .suit-price {
                font-size: 10px;
                color: #fa8c16;
                font-weight: 600;
              }
            }
          }

          .suit-actions {
            flex-shrink: 0;
            opacity: 0;
            transition: opacity 0.15s;

            .ant-btn {
              padding: 0 4px;
              height: 20px;
              font-size: 11px;
              line-height: 1.2;
            }
          }
        }

        &:hover .suit-actions {
          opacity: 1;
        }
      }
    }
  }
</style>
