import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';

const { createConfirm } = useMessage();

enum Api {
  list = '/basicinfo/zyConclusionDict/list',
  save = '/basicinfo/zyConclusionDict/add',
  edit = '/basicinfo/zyConclusionDict/edit',
  deleteOne = '/basicinfo/zyConclusionDict/delete',
  deleteBatch = '/basicinfo/zyConclusionDict/deleteBatch',
  importExcel = '/basicinfo/zyConclusionDict/importExcel',
  exportXls = '/basicinfo/zyConclusionDict/exportXls',
  listZyAdvice = '/occu/zyAdviceDict/listZyAdvice',
  increaseUseCount = '/basicinfo/zyConclusionDict/increaseUseCount',
  defaultValue = '/basicinfo/zyConclusionDict/defaultValue',
}

/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;

/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};

/**
 * 保存或者更新
 * @param params
 * @param isUpdate
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params }, { isTransformResponse: false });
};

/**
 * 根据字典类型查询字典数据
 * @param params
 */
export const listZyAdvice = () => defHttp.get({ url: Api.listZyAdvice });

/**
 * 增加使用次数
 * @param params
 */
export const increaseUseCount = (params) => defHttp.get({ url: Api.increaseUseCount, params }, { isTransformResponse: false });

export const defaultValue = () => defHttp.get({ url: Api.defaultValue });
