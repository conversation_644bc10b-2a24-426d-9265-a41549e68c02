<template>
  <a-modal v-model:open="visible" title="回收站" width="80%" destroyOnClose @ok="handleOk" @cancel="handleOk" cancelText="关闭">
    <div style="height: 100%" :class="[`${prefixCls}`]">
      <div class="p-2">
        <!--查询区域-->
        <div class="jeecg-basic-table-form-container">
          <a-form ref="formRef" @keyup.enter="searchQuery" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
            <a-row :gutter="24">
              <a-col :lg="9">
                <a-form-item name="name">
                  <template #label><span title="组合名称">名称或助记码</span></template>
                  <a-input placeholder="请输入组合名称" v-model:value="queryParam.keyword" />
                </a-form-item>
              </a-col>
              <a-col :xl="6" :lg="6" :md="6" :sm="24">
                <a-space>
                  <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery">查询</a-button>
                  <a-button type="primary" preIcon="ant-design:reload-outlined" @click="searchReset" style="margin-left: 8px">重置</a-button>
                </a-space>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <!--引用表格-->
        <BasicTable @register="registerTable" :rowSelection="rowSelection">
          <!--插槽:table标题-->
          <template #tableTitle>
            <a-dropdown v-if="selectedRowKeys.length > 0">
              <template #overlay>
                <a-menu>
                  <a-menu-item key="1" @click="batchHandleRecycle">
                    <Icon icon="ant-design:delete-outlined" />
                    恢复
                  </a-menu-item>
                  <a-menu-item key="1" @click="batchHandleDelete">
                    <Icon icon="ant-design:delete-outlined" />
                    永久删除
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button
                >批量操作
                <Icon icon="mdi:chevron-down" />
              </a-button>
            </a-dropdown>
          </template>
          <!--操作栏-->
          <template #action="{ record }">
            <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
          </template>
          <template #bodyCell="{ column, record, index, text }"> </template>
        </BasicTable>
        <!-- 表单区域 -->
        <SuitDetail ref="registerDetail" />
      </div>
    </div>
  </a-modal>
</template>

<script lang="ts" name="basicinfo-itemGroup-recycle" setup>
  import { reactive, ref } from 'vue';
  import { BasicTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { columns, superQuerySchema } from './ItemSuit.data';
  import { deleteBatchForever, listSuitRecycleBin, recoverBatch } from './ItemSuit.api';
  import { useUserStore } from '/@/store/modules/user';
  import { message } from 'ant-design-vue';
  import SuitDetail from '@/views/basicinfo/components/SuitDetail.vue';

  const formRef = ref();
  const queryParam = reactive<any>({});
  const toggleSearchStatus = ref<boolean>(false);
  const registerDetail = ref();
  const userStore = useUserStore();
  const visible = ref<boolean>(false);
  const emit = defineEmits(['success']);

  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: '体检套餐回收站',
      size: 'small',
      api: listSuitRecycleBin,
      columns,
      canResize: false,
      useSearchForm: false,
      actionColumn: {
        width: 120,
        fixed: 'right',
      },
    },
  });
  const [registerTable, { reload, collapseAll, updateTableDataRecord, findTableDataRecord, getDataSource }, { rowSelection, selectedRowKeys }] =
    tableContext;
  const labelCol = reactive({
    xs: 24,
    sm: 6,
    xl: 6,
    xxl: 6,
  });
  const wrapperCol = reactive({
    xs: 24,
    sm: 18,
  });

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    searchQuery();
  }

  /**
   * 编辑事件
   */
  function handleRecover(record: Recordable) {
    recoverBatch({ ids: record.id }, handleSuccess);
  }

  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    registerDetail.value.showDetail(record);
  }

  /**
   * 批量删除事件
   */
  async function batchHandleRecycle() {
    if (selectedRowKeys.value.length === 0) {
      message.error('请选择需要回收的数据');
      return;
    }
    recoverBatch({ ids: selectedRowKeys.value.join(',') }, handleSuccess);
  }

  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }

  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '恢复',
        onClick: handleRecover.bind(null, record),
      },
    ];
  }

  /**
   * 下拉操作栏
   */
  function getDropDownAction(record) {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      },
      {
        label: '永久删除',
        popConfirm: {
          title: '是否确认永久删除',
          confirm: handleDelete.bind(null, record),
          placement: 'topLeft',
        },
      },
    ];
  }

  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteBatchForever({ ids: record.id }, handleSuccess);
  }

  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    await deleteBatchForever({ ids: selectedRowKeys.value }, handleSuccess);
  }

  /**
   * 查询
   */
  function searchQuery() {
    reload({ page: 1 });
  }

  /**
   * 重置
   */
  function searchReset() {
    selectedRowKeys.value = [];
    //刷新数据
    reload();
  }

  function handleOk() {
    emit('success');
    visible.value = false;
  }
  function openPage() {
    visible.value = true;
  }
  defineExpose({
    openPage,
  });
</script>

<style lang="less" scoped>
  .jeecg-basic-table-form-container {
    padding: 0;
    .table-page-search-submitButtons {
      display: block;
      margin-bottom: 12px;
      white-space: nowrap;
    }
    .query-group-cust {
      min-width: 100px !important;
    }
    .query-group-split-cust {
      width: 30px;
      display: inline-block;
      text-align: center;
    }
  }
</style>
