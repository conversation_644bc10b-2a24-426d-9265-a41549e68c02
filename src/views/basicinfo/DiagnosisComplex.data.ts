import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '结论',
    align: 'center',
    dataIndex: 'name',
  },
  {
    title: '科室',
    align: 'center',
    dataIndex: 'departmentId_dictText',
  },
  {
    title: '最小年龄',
    align: 'center',
    dataIndex: 'minAge',
  },
  {
    title: '最大年龄',
    align: 'center',
    dataIndex: 'maxAge',
  },
  {
    title: '适用性别',
    align: 'center',
    dataIndex: 'genderLimit_dictText',
  },
  {
    title: '适用婚别',
    align: 'center',
    dataIndex: 'marriageLimit_dictText',
  },
  {
    title: '启用',
    align: 'center',
    dataIndex: 'enableFlag',
    customRender: ({ text }) => {
      return render.renderSwitch(text, [
        { text: '是', value: '1' },
        { text: '否', value: '0' },
      ]);
    },
  },
  {
    title: '计算公式',
    align: 'center',
    dataIndex: 'formula',
  },
  {
    title: '创建人',
    align: 'center',
    dataIndex: 'createBy',
  },
  {
    title: '创建时间',
    align: 'center',
    dataIndex: 'createTime',
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '科室ID',
    field: 'departmentId',
    component: 'JSelectDept',
    //colProps: {span: 6},
  },
  {
    label: '启用',
    field: 'enableFlag',
    component: 'JSwitch',
    componentProps: {
      query: true,
      options: [1, 0],
    },
    //colProps: {span: 6},
  },
];

//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '科室ID',
    field: 'departmentId',
    component: 'JSelectDept',
  },
  {
    label: '结论',
    field: 'name',
    component: 'Input',
  },
  {
    label: '最小年龄',
    field: 'minAge',
    component: 'InputNumber',
  },
  {
    label: '最大年龄',
    field: 'maxAge',
    component: 'InputNumber',
  },
  {
    label: '适用性别',
    field: 'genderLimit',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'sexLimit',
    },
  },
  {
    label: '适用婚别',
    field: 'marriageLimit',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'material_type',
    },
  },
  {
    label: '启用',
    field: 'enableFlag',
    component: 'JSwitch',
    componentProps: {
      options: [1, 0],
    },
  },
  {
    label: '计算公式',
    field: 'formula',
    component: 'InputTextArea', //TODO 注意string转换问题
  },
  {
    label: 'createBy',
    field: 'createBy',
    component: 'Input',
    dynamicDisabled: true,
  },
  {
    label: 'createTime',
    field: 'createTime',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    dynamicDisabled: true,
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];

//子表列表数据
export const diagnosisComplexDataColumns: BasicColumn[] = [
  {
    title: 'ruleId',
    align: 'center',
    dataIndex: 'ruleId',
  },
  {
    title: 'itemId',
    align: 'center',
    dataIndex: 'itemId',
  },
  {
    title: '项目',
    align: 'center',
    dataIndex: 'itemName_dictText',
  },
  {
    title: '项目类型',
    align: 'center',
    dataIndex: 'itemType',
  },
  {
    title: '字段名',
    align: 'center',
    dataIndex: 'fieldName',
  },
  {
    title: '字段类型',
    align: 'center',
    dataIndex: 'fieldType',
  },
  {
    title: '字段值',
    align: 'center',
    dataIndex: 'fieldValue',
  },
  {
    title: '操作符',
    align: 'center',
    dataIndex: 'operator_dictText',
  },
];
//子表表单数据
export const diagnosisComplexDataFormSchema: FormSchema[] = [
  // TODO 子表隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    label: 'ruleId',
    field: 'ruleId',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入ruleId!' }];
    },
  },
  {
    label: 'itemId',
    field: 'itemId',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入itemId!' }];
    },
  },
  {
    label: '项目类型',
    field: 'itemType',
    component: 'InputNumber',
  },
  {
    label: '字段名',
    field: 'fieldName',
    component: 'Input',
  },
  {
    label: '字段类型',
    field: 'fieldType',
    component: 'Input',
  },
  {
    label: '字段值',
    field: 'fieldValue',
    component: 'Input',
  },
  {
    label: '操作符',
    field: 'operator',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: '',
    },
  },
];

// 高级查询数据
export const superQuerySchema = {
  departmentName: { title: '科室', order: 1, view: 'text', type: 'string' },
  name: { title: '诊断名称', order: 2, view: 'text', type: 'string' },
  minAge: { title: '最小年龄', order: 3, view: 'number', type: 'number' },
  maxAge: { title: '最大年龄', order: 4, view: 'number', type: 'number' },
  genderLimit: { title: '适用性别', order: 5, view: 'list', type: 'string', dictCode: 'sexLimit' },
  marriageLimit: { title: '适用婚别', order: 5, view: 'list', type: 'string', dictCode: 'material_type' },
  enableFlag: { title: '启用', order: 6, view: 'number', type: 'number' },
  formula: { title: '计算公式', order: 7, view: 'textarea', type: 'string' },
  createBy: { title: 'createBy', order: 8, view: 'text', type: 'string' },
  creatTime: { title: 'creatTime', order: 9, view: 'datetime', type: 'string' },
};
