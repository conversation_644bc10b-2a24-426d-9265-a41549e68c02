import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [

  {
    title: '大项名称',
    align: "center",
    dataIndex: 'groupName'
  },
  {
    title: '旧价格',
    align: "center",
    dataIndex: 'oldPrice'
  },
  {
    title: '新价格',
    align: "center",
    dataIndex: 'newPrice'
  },
  {
    title: '变动原因',
    align: "center",
    dataIndex: 'changeReason'
  },
  {
    title: '操作人工号',
    align: "center",
    dataIndex: 'operator'
  },
  {
    title: '操作人',
    align: "center",
    dataIndex: 'operatorName'
  },
  {
    title: '操作时间',
    align: "center",
    sorter: true,
    dataIndex: 'operateTime'
  },
];

// 高级查询数据
export const superQuerySchema = {
  groupId: {title: '大项id',order: 0,view: 'text', type: 'string',},
  groupName: {title: '大项名称',order: 1,view: 'text', type: 'string',},
  oldPrice: {title: '旧价格',order: 2,view: 'number', type: 'number',},
  newPrice: {title: '新价格',order: 3,view: 'number', type: 'number',},
  changeReason: {title: '变动原因',order: 4,view: 'textarea', type: 'string',},
  operator: {title: '操作人',order: 5,view: 'text', type: 'string',},
  operateTime: {title: '操作时间',order: 6,view: 'datetime', type: 'string',},
};
