<template>
  <div style="padding: 5px">
    <a-row :gutter="4">
      <a-col :span="6">
        <a-card size="small" title="体检人员列表" style="padding: 0; min-height: 90vh">
          <customer-reg-list4-interface-check ref="customerRegList" @row-click="fetchDepartList" />
        </a-card>
      </a-col>
      <a-col :span="18" style="background-color: #ffffff">
        <a-tabs v-model:activeKey="activeTabKey" @change="handleTabChange">
          <a-tab-pane key="interfaceStatus" tab="项目状态">
            <a-tabs v-model:activeKey="currentDepartmentId" @change="handleDepartTabChange" style="margin-top: 5px">
              <a-tab-pane :key="item.departmentId" v-for="(item, index) in departStatList">
                <template #tab>
                  <span>{{ item.departmentName }}</span>
                </template>
              </a-tab-pane>
            </a-tabs>
            <RegItemGroupList4InterfaceCheck ref="regItemGroupList" @success="reloadCustomerRegList" />
          </a-tab-pane>
          <!--          <a-tab-pane key="interfaceLog" tab="接口记录">
            <InterfaceTraceLogList4Reg ref="interfaceLogListRef"  />
          </a-tab-pane>-->
        </a-tabs>
      </a-col>
    </a-row>
  </div>
</template>

<script lang="ts" setup name="InterfaceCheckPannel">
  import { computed, nextTick, ref, watch } from 'vue';
  import { DepartStat, ICustomerReg } from '#/types';
  import { useMessage } from '@/hooks/web/useMessage';
  import RegItemGroupList4InterfaceCheck from '@/views/interfacecheck/RegItemGroupList4InterfaceCheck.vue';
  import CustomerRegList4InterfaceCheck from '@/views/interfacecheck/CustomerRegList4InterfaceCheck.vue';
  import { regGroupsGroupByDepart } from '@/views/interfacecheck/RegInterfaceCheck.api';
  import InterfaceTraceLogList4Reg from '@/views/interfacecheck/InterfaceTraceLogList4Reg.vue';

  const { createConfirm, createErrorModal } = useMessage();
  const loading = ref(false);

  const departStatList = ref<DepartStat[]>([]);
  const activeTabKey = ref('interfaceStatus');
  const currentDepartmentId = ref('');
  const currentDepart = computed(() => {
    return departStatList.value.find((item) => item.departmentId === currentDepartmentId.value)?.depart;
  });
  const customerRegList = ref(null);
  const currentReg = ref<ICustomerReg>({});

  const regItemGroupList = ref(null);
  const interfaceLogListRef = ref(null);

  function reloadCustomerRegList() {
    customerRegList.value?.reloadCurrentRow();
  }

  function handleTabChange(key) {
    activeTabKey.value = key;
    if (key === 'interfaceStatus') {
      handleDepartTabChange(currentDepartmentId.value);
    } else if (key === 'interfaceLog') {
      interfaceLogListRef.value?.loadData(currentReg.value);
    }
  }

  function handleDepartTabChange(key) {
    currentDepartmentId.value = key;
    regItemGroupList.value?.loadData(currentReg.value, currentDepart.value);
  }

  function fetchDepartList(selectedCustomerReg: ICustomerReg) {
    activeTabKey.value = 'interfaceStatus';
    currentReg.value = selectedCustomerReg;
    let reqParam = { customerRegId: currentReg.value.id };
    loading.value = true;
    regGroupsGroupByDepart(reqParam)
      .then((res) => {
        departStatList.value = res;
        if (res.length > 0) {
          nextTick(() => {
            currentDepartmentId.value = res[0].departmentId;
            regItemGroupList.value?.loadData(currentReg.value, currentDepart.value);
          });
        }
      })
      .finally(() => {
        loading.value = false;
      });
  }
</script>
<style scoped>
  .abandon {
    text-decoration: line-through #ff4d4f;
  }

  .active-border {
    animation: glow 800ms ease-out infinite alternate;
  }

  @keyframes glow {
    0% {
      border-color: #0a8fe9;
      box-shadow: 0 0 5px rgba(10, 143, 233, 0.2);
    }
    100% {
      border-color: #0a8fe9;
      box-shadow: 0 0 20px rgba(10, 143, 233, 0.6);
    }
  }

  .error-modal {
    max-height: 50vh;
    overflow-y: scroll;
  }
  .done {
    border-left: #00db00 solid 2px;
  }
  .wait {
    border-left: #ff4d4f solid 2px;
  }
</style>
