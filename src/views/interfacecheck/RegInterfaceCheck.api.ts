import { defHttp } from '/src/utils/http/axios';

enum Api {
  getInterfaceResultByRegGroupId = '/interfaceCheck/regCheck/getInterfaceResultByRegGroupId',
  listReg4Interface = '/interfaceCheck/regCheck/list',
  regGroupsGroupByDepart = '/interfaceCheck/regCheck/regGroupsGroupByDepart',
  getGroupsByRegIdAndDepartId = '/interfaceCheck/regCheck/getGroupsByRegIdAndDepartId',
  handleBatch = '/interfaceCheck/interfaceTraceLog/handleBatch',
}

/**
 * 列表接口
 * @param params
 */
export const listReg4Interface = (params) => defHttp.get({ url: Api.listReg4Interface, params });

export const getGroupsByRegIdAndDepartId = (params) => {
  return defHttp.get({ url: Api.getGroupsByRegIdAndDepartId, params });
};

export const getInterfaceResultByRegGroupId = (params) => {
  return defHttp.get({ url: Api.getInterfaceResultByRegGroupId, params }, { isTransformResponse: false });
};

export const regGroupsGroupByDepart = (params) => {
  return defHttp.get({ url: Api.regGroupsGroupByDepart, params });
};

export const handleBatch = (params, handleSuccess) => {
  return defHttp.post({ url: Api.handleBatch, data: params }).then(() => {
    handleSuccess();
  });
};
