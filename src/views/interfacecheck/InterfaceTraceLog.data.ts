import { BasicColumn } from '/src/components/Table';
import { FormSchema } from '/src/components/Table';
import { rules } from '/src/utils/helper/validator';
import { render } from '/src/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/src/utils';
//列表数据
export const columns4Reg: BasicColumn[] = [
  {
    title: '时间',
    align: 'center',
    dataIndex: 'createTime',
  },
  {
    title: '响应时长',
    align: 'center',
    dataIndex: 'costTime',
  },
  {
    title: '接口名称',
    align: 'center',
    dataIndex: 'description',
  },
  {
    title: '类型',
    align: 'center',
    dataIndex: 'type',
  },
  {
    title: '接口状态',
    align: 'center',
    dataIndex: 'okFlag',
  },
  {
    title: '请求体',
    align: 'center',
    dataIndex: 'reqBody',
  },
  {
    title: '响应体',
    align: 'center',
    dataIndex: 'respBody',
  },
  {
    title: '报错信息',
    align: 'center',
    dataIndex: 'errorMsg',
  },
];
export const columns: BasicColumn[] = [
  {
    title: '接口名称',
    align: 'center',
    dataIndex: 'description',
    width: 150,
  },
  {
    title: '创建时间',
    align: 'center',
    dataIndex: 'createTime',
    width: 150,
  },
  {
    title: '响应状态',
    align: 'center',
    dataIndex: 'respStatus',
    width: 100,
  },
  {
    title: '处理状态',
    align: 'center',
    dataIndex: 'okFlag',
    width: 100,
  },
  {
    title: '响应时长',
    align: 'center',
    dataIndex: 'respCostTime',
    width: 100,
    customRender: ({ text }) => {
      return text + 'ms';
    },
  },
  {
    title: '处理时长',
    align: 'center',
    dataIndex: 'costTime',
    width: 100,
    customRender: ({ text }) => {
      return text + 'ms';
    },
  },
  {
    title: '姓名',
    align: 'center',
    dataIndex: 'name',
    width: 100,
  },
  {
    title: '体检号',
    align: 'center',
    dataIndex: 'examNo',
    width: 100,
  },
  {
    title: '类型',
    align: 'center',
    dataIndex: 'type',
    width: 100,
  },
  {
    title: '接口路径',
    align: 'center',
    dataIndex: 'reqUrl',
    width: 100,
  },
  {
    title: '请求体',
    align: 'center',
    dataIndex: 'reqBody',
  },
  {
    title: '响应体',
    align: 'center',
    dataIndex: 'respBody',
  },
  {
    title: '报错信息',
    align: 'center',
    dataIndex: 'errorMsg',
  },
];

// 高级查询数据
export const superQuerySchema = {
  examNo: { title: '体检号', order: 0, view: 'text', type: 'string' },
  type: { title: '类型（查询、更新状态、报告回推）', order: 1, view: 'text', type: 'string' },
  createTime: { title: '创建时间', order: 2, view: 'datetime', type: 'string' },
  description: { title: '接口名称', order: 4, view: 'text', type: 'string' },
  costTime: { title: '响应时长', order: 5, view: 'number', type: 'number' },
  reqBody: { title: '请求体', order: 6, view: 'textarea', type: 'string' },
  respBody: { title: '响应体', order: 7, view: 'textarea', type: 'string' },
  errorMsg: { title: '报错信息', order: 8, view: 'text', type: 'string' },
  okFlag: { title: '报错信息', order: 9, view: 'text', type: 'string' },
};
