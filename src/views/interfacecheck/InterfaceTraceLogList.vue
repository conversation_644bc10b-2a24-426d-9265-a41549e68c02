<template>
  <div class="p-2">
    <!--查询区域-->
    <div class="jeecg-basic-table-form-container">
      <a-form ref="formRef" @keyup.enter="searchQuery" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-row :gutter="24">
          <a-col :lg="5">
            <a-form-item name="createTime">
              <template #label><span title="时间">时间</span></template>
              <a-range-picker
                v-model:value="regDateRange"
                placement="登记日期"
                style="width: 100%"
                @change="searchQuery"
                :presets="rangePresets"
                :allow-clear="false"
              />
            </a-form-item>
          </a-col>
          <a-col :lg="3">
            <a-form-item name="name">
              <template #label><span title="姓名">姓名</span></template>
              <a-input placeholder="请输入姓名" v-model:value="queryParam.name" :allowClear="true" />
            </a-form-item>
          </a-col>
          <a-col :lg="4">
            <a-form-item name="examNo">
              <template #label><span title="体检号">体检号</span></template>
              <a-input placeholder="请输入体检号" v-model:value="queryParam.examNo" :allowClear="true" />
            </a-form-item>
          </a-col>
          <a-col :lg="4">
            <a-form-item name="description">
              <template #label><span title="接口名称">接口名称</span></template>
              <j-dict-select-tag v-model:value="queryParam.description" :dictCode="'interface_desc'" />
            </a-form-item>
          </a-col>

          <a-col :lg="4">
            <a-form-item name="okFlag">
              <template #label><span title="接口状态">接口状态</span></template>
              <a-select v-model:value="queryParam.okFlag">
                <a-select-option value="">全部</a-select-option>
                <a-select-option value="正常">正常</a-select-option>
                <a-select-option value="异常">异常</a-select-option>
                <a-select-option value="已处理">已处理</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xl="4" :lg="4" :md="4" :sm="24">
            <a-space>
              <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery">查询</a-button>
              <a-button type="primary" preIcon="ant-design:reload-outlined" @click="searchReset" style="margin-left: 8px">重置</a-button>
            </a-space>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" v-auth="'station:interface_trace_log:exportXls'" preIcon="ant-design:export-outlined" @click="onExportXls">
          导出</a-button
        >
        <a-button type="primary" v-auth="'station:interface_trace_log:handleBatch'" preIcon="ant-design:setting-outlined" @click="openHandleResult">
          处理</a-button
        >
        <!-- 高级查询 -->
        <super-query :config="superQueryConfig" @search="handleSuperQuery" />
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
      <template #bodyCell="{ column, record, index, text }"> </template>
    </BasicTable>
    <!-- 表单区域 -->
    <InterfaceTraceLogModal ref="registerModal" @success="handleSuccess" />
    <a-modal v-model:open="handleResultVisiable" title="处理结果" width="50%" @ok="handleResult" @cancel="handleCancel">
      <div style="height: 50vh; overflow-y: auto; padding: 20px">
        <a-form :model="handleResultForm" :label-col="labelCol" :wrapper-col="wrapperCol">
          <a-row :gutter="24">
            <a-col :lg="24">
              <a-form-item label="处理状态" id="InterfaceTraceLogForm-okFlag" name="okFlag">
                <a-select v-model:value="handleResultForm.okFlag">
                  <a-select-option value="已处理">已处理</a-select-option>
                  <a-select-option value="未处理">异常</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :lg="24">
              <a-form-item label="处理描述" id="InterfaceTraceLogForm-handleResult" name="handleResult">
                <a-input v-model:value="handleResultForm.handleResult" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" name="datasync-interfaceTraceLog" setup>
  import { computed, reactive, ref } from 'vue';
  import { BasicTable, TableAction } from '/src/components/Table';
  import { useListPage } from '/src/hooks/system/useListPage';
  import { columns, superQuerySchema } from './InterfaceTraceLog.data';
  import { batchDelete, deleteOne, getExportUrl, getImportUrl, list } from './InterfaceTraceLog.api';
  import InterfaceTraceLogModal from './components/InterfaceTraceLogModal.vue';
  import { useUserStore } from '/src/store/modules/user';
  import { ICustomerReg, type RangeValue } from '#/types';
  import JDictSelectTag from '@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import dayjs from 'dayjs';
  import { message } from 'ant-design-vue';
  import { handleBatch } from '@/views/interfacecheck/RegInterfaceCheck.api';

  const formRef = ref();
  const queryParam = reactive<any>({
    okFlag: '异常',
  });
  const toggleSearchStatus = ref<boolean>(false);
  const registerModal = ref();
  const userStore = useUserStore();
  const regDateRange = ref<RangeValue>([dayjs(), dayjs()]);
  const rangePresets = ref([
    { label: '今天', value: [dayjs(), dayjs()] },
    { label: '过去一周', value: [dayjs().add(-7, 'd'), dayjs()] },
    { label: '过去二周', value: [dayjs().add(-14, 'd'), dayjs()] },
    { label: '过去30天', value: [dayjs().add(-30, 'd'), dayjs()] },
    { label: '过去90天', value: [dayjs().add(-90, 'd'), dayjs()] },
    { label: '过去一年', value: [dayjs().add(-1, 'y'), dayjs()] },
    { label: '过去两年', value: [dayjs().add(-2, 'y'), dayjs()] },
    { label: '不限', value: null },
  ]);

  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: 'interface_trace_log',
      api: list,
      columns,
      canResize: false,
      useSearchForm: false,
      actionColumn: {
        width: 120,
        fixed: 'right',
      },
      beforeFetch: async (params) => {
        if (regDateRange.value && regDateRange.value.length == 2) {
          queryParam.createTimeStart = regDateRange.value[0].format('YYYY-MM-DD') + ' 00:00:00';
          queryParam.createTimeEnd = regDateRange.value[1].format('YYYY-MM-DD') + ' 23:59:59';
        }
        return Object.assign(params, queryParam);
      },
    },
    exportConfig: {
      name: 'interface_trace_log',
      url: getExportUrl,
      params: queryParam,
    },
    importConfig: {
      url: getImportUrl,
      success: handleSuccess,
    },
  });
  const [
    registerTable,
    { reload, collapseAll, updateTableDataRecord, findTableDataRecord, getDataSource },
    { rowSelection, selectedRowKeys, selectedRows },
  ] = tableContext;
  const labelCol = reactive({
    xs: 24,
    sm: 6,
    xl: 6,
    xxl: 6,
  });
  const wrapperCol = reactive({
    xs: 24,
    sm: 18,
  });

  // 处理结果
  const handleResultVisiable = ref<boolean>(false);
  const handleResultForm = reactive<any>({
    okFlag: '已处理',
    handleResult: '',
  });
  const handleResult = () => {
    if (!handleResultForm.handleResult) {
      message.error('处理信息不能为空');
      return;
    }

    selectedRows.value.forEach((item) => {
      item.okFlag = handleResultForm.okFlag;
      item.handleResult = handleResultForm.handleResult;
    });

    let data = selectedRows.value.map((item) => {
      return {
        id: item.id,
        okFlag: item.okFlag,
        handleResult: item.handleResult,
      };
    });

    handleBatch(data, reloadPage);
  };
  const handleCancel = () => {
    handleResultVisiable.value = false;
  };
  const openHandleResult = () => {
    //判断是否选择了数据，并且okFlag为异常
    if (selectedRows.value.length == 0) {
      message.error('请选择数据');
      return;
    }
    if (selectedRows.value.filter((item) => item.okFlag == '异常').length == 0) {
      message.error('请选择异常数据');
      return;
    }
    handleResultVisiable.value = true;
  };

  function reloadPage() {
    reload({ page: 1 });
    handleResultVisiable.value = false;
    selectedRows.value = [];
    selectedRowKeys.value = [];
  }

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);
  async function loadData(reg: ICustomerReg) {
    console.log(JSON.stringify(reg));
    if (reg?.examNo) {
      queryParam.examNo = reg.examNo;
      //刷新数据
      reload();
    }
  }

  const hasQueryCondition = computed(() => {
    return queryParam.name || queryParam.examNo || queryParam.okFlag;
  });
  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    searchQuery();
  }

  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    registerModal.value.disableSubmit = true;
    registerModal.value.edit(record);
  }

  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteOne({ id: record.id }, handleSuccess);
  }

  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    await batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
  }

  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }

  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      },
    ];
  }

  /**
   * 下拉操作栏
   */
  function getDropDownAction(record) {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      },
      {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
          placement: 'topLeft',
        },
        auth: 'datasync:interface_trace_log:delete',
      },
    ];
  }

  /**
   * 查询
   */
  function searchQuery() {
    reload({ page: 1 });
  }

  /**
   * 重置
   */
  function searchReset() {
    formRef.value.resetFields();
    selectedRowKeys.value = [];
    //刷新数据
    reload();
  }

  defineExpose({
    loadData,
  });
</script>

<style lang="less" scoped>
  .jeecg-basic-table-form-container {
    padding: 0;
    .table-page-search-submitButtons {
      display: block;
      margin-bottom: 24px;
      white-space: nowrap;
    }
    .query-group-cust {
      min-width: 100px !important;
    }
    .query-group-split-cust {
      width: 30px;
      display: inline-block;
      text-align: center;
    }
    .ant-form-item:not(.ant-form-item-with-help) {
      margin-bottom: 16px;
      height: 32px;
    }
    :deep(.ant-picker),
    :deep(.ant-input-number) {
      width: 100%;
    }
  }
</style>
