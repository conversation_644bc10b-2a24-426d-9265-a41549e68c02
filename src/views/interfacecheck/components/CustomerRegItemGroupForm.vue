<template>
  <a-spin :spinning="confirmLoading">
    <JFormContainer :disabled="disabled">
      <template #detail>
        <a-form ref="formRef" class="antd-modal-form" :labelCol="labelCol" :wrapperCol="wrapperCol" name="CustomerRegItemGroupForm">
          <a-row>
            <a-col :span="24">
              <a-form-item label="组合名称" v-bind="validateInfos.itemGroupName" id="CustomerRegItemGroupForm-itemGroupName" name="itemGroupName">
                <a-input v-model:value="formData.itemGroupName" placeholder="请输入组合名称" />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="组合类型" v-bind="validateInfos.classCode" id="CustomerRegItemGroupForm-classCode" name="classCode">
                <a-input v-model:value="formData.classCode" placeholder="请输入组合类型" />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="折后价" v-bind="validateInfos.priceAfterDis" id="CustomerRegItemGroupForm-priceAfterDis" name="priceAfterDis">
                <a-input-number v-model:value="formData.priceAfterDis" placeholder="请输入折后价" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="支付方" v-bind="validateInfos.payerType" id="CustomerRegItemGroupForm-payerType" name="payerType">
                <a-input v-model:value="formData.payerType" placeholder="请输入支付方" />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="支付状态" v-bind="validateInfos.payStatus" id="CustomerRegItemGroupForm-payStatus" name="payStatus">
                <a-input v-model:value="formData.payStatus" placeholder="请输入支付状态" />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="检查状态" v-bind="validateInfos.checkStatus" id="CustomerRegItemGroupForm-checkStatus" name="checkStatus">
                <a-input v-model:value="formData.checkStatus" placeholder="请输入检查状态" />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item
                label="接口状态"
                v-bind="validateInfos.interfaceSyncStatus"
                id="CustomerRegItemGroupForm-interfaceSyncStatus"
                name="interfaceSyncStatus"
              >
                <a-input v-model:value="formData.interfaceSyncStatus" placeholder="请输入检查状态" />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="接口调用时间" v-bind="validateInfos.createTime" id="CustomerRegItemGroupForm-createTime" name="createTime">
                <a-input v-model:value="formData.createTime" placeholder="请输入接口调用时间" />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="接口返回内容" v-bind="validateInfos.interfaceText" id="CustomerRegItemGroupForm-interfaceText" name="interfaceText">
                <a-textarea :auto-size="{ minRows: 2 }" v-model:value="formData.interfaceText" placeholder="接口返回内容" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </template>
    </JFormContainer>
  </a-spin>
</template>

<script lang="ts" setup>
  import { ref, reactive, defineExpose, nextTick, defineProps, computed, onMounted } from 'vue';
  import { defHttp } from '/src/utils/http/axios';
  import { useMessage } from '/src/hooks/web/useMessage';
  import { getValueType } from '/src/utils';
  import { saveOrUpdate } from '../../station/CustomerRegItemGroup.api';
  import { Form } from 'ant-design-vue';
  import JFormContainer from '/src/components/Form/src/container/JFormContainer.vue';
  const props = defineProps({
    formDisabled: { type: Boolean, default: false },
    formData: { type: Object, default: () => ({}) },
    formBpm: { type: Boolean, default: true },
  });
  const formRef = ref();
  const useForm = Form.useForm;
  const emit = defineEmits(['register', 'ok']);
  const formData = reactive<Record<string, any>>({
    id: '',
    customerRegId: '',
    itemGroupId: '',
    itemGroupName: '',
    departmentId: '',
    departmentName: '',
    addMinusFlag: undefined,
    price: undefined,
    disRate: undefined,
    priceAfterDis: undefined,
    payerType: '',
    payStatus: '',
    checkTime: '',
    checkStatus: '',
    classCode: '',
    interfaceSyncStatus: '',
    createTime: '',
    interfaceText: '',
  });
  const { createMessage } = useMessage();
  const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 5 } });
  const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 16 } });
  const confirmLoading = ref<boolean>(false);
  //表单验证
  const validatorRules = reactive({
    customerRegId: [{ required: true, message: '请输入登记ID!' }],
    itemGroupId: [{ required: true, message: '请输入组合ID!' }],
    departmentId: [{ required: true, message: '请输入科室ID!' }],
  });
  const { resetFields, validate, validateInfos } = useForm(formData, validatorRules, { immediate: false });

  // 表单禁用
  const disabled = computed(() => {
    if (props.formBpm === true) {
      if (props.formData.disabled === false) {
        return false;
      } else {
        return true;
      }
    }
    return props.formDisabled;
  });

  /**
   * 新增
   */
  function add() {
    edit({});
  }

  /**
   * 编辑
   */
  function edit(record) {
    nextTick(() => {
      resetFields();
      const tmpData = {};
      Object.keys(formData).forEach((key) => {
        if (record.hasOwnProperty(key)) {
          tmpData[key] = record[key];
        }
      });
      //赋值
      Object.assign(formData, tmpData);
    });
  }

  /**
   * 提交数据
   */
  async function submitForm() {
    try {
      // 触发表单验证
      await validate();
    } catch ({ errorFields }) {
      if (errorFields) {
        const firstField = errorFields[0];
        if (firstField) {
          formRef.value.scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
        }
      }
      return Promise.reject(errorFields);
    }
    confirmLoading.value = true;
    const isUpdate = ref<boolean>(false);
    //时间格式化
    let model = formData;
    if (model.id) {
      isUpdate.value = true;
    }
    //循环数据
    for (let data in model) {
      //如果该数据是数组并且是字符串类型
      if (model[data] instanceof Array) {
        let valueType = getValueType(formRef.value.getProps, data);
        //如果是字符串类型的需要变成以逗号分割的字符串
        if (valueType === 'string') {
          model[data] = model[data].join(',');
        }
      }
    }
    await saveOrUpdate(model, isUpdate.value)
      .then((res) => {
        if (res.success) {
          createMessage.success(res.message);
          emit('ok');
        } else {
          createMessage.warning(res.message);
        }
      })
      .finally(() => {
        confirmLoading.value = false;
      });
  }

  defineExpose({
    add,
    edit,
    submitForm,
  });
</script>

<style lang="less" scoped>
  .antd-modal-form {
    padding: 14px;
  }
</style>
