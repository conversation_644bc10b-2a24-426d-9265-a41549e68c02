<template>
  <a-spin :spinning="confirmLoading">
    <JFormContainer>
      <template #detail>
        <a-form ref="formRef" class="antd-modal-form" :labelCol="labelCol" :wrapperCol="wrapperCol" name="InterfaceTraceLogForm">
          <a-row>
            <a-col :span="24">
              <a-form-item label="体检号">
                <a-typography-text>{{ formData.examNo }}</a-typography-text>
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="姓名">
                <a-typography-text>{{ formData.name }}</a-typography-text>
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="接口名称">
                <a-typography-text>{{ formData.description }}</a-typography-text>
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="接口类型">
                <a-typography-text>{{ formData.type }}</a-typography-text>
              </a-form-item>
            </a-col>

            <a-col :span="24">
              <a-form-item label="接口状态">
                <a-typography-text>{{ formData.okFlag }}</a-typography-text>
              </a-form-item>
            </a-col>
            <template v-if="formData.okFlag == '已处理'">
              <a-col :span="24">
                <a-form-item label="处理人">
                  <a-typography-text>{{ formData.handleBy }}</a-typography-text>
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item label="处理时间">
                  <a-typography-text>{{ formData.handleTime }}</a-typography-text>
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item label="处理描述">
                  <a-typography-text>{{ formData.handleResult }}</a-typography-text>
                </a-form-item>
              </a-col>
            </template>

            <a-col :span="24">
              <a-form-item label="响应时长">
                <a-typography-text>{{ formData.respTime }}ms</a-typography-text>
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="处理时长">
                <a-typography-text>{{ formData.costTime }}ms</a-typography-text>
              </a-form-item>
            </a-col>

            <a-col :span="24">
              <a-form-item label="响应状态">
                <a-typography-text>{{ formData.respStatus }}</a-typography-text>
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="创建时间">
                <a-typography-text>{{ formData.createTime }}</a-typography-text>
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="请求体">
                <a-typography-text>{{ formData.reqBody }}</a-typography-text>
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="响应体">
                <a-typography-text>{{ formData.respBody }}</a-typography-text>
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="报错信息">
                <a-typography-text>{{ formData.errorMsg }}</a-typography-text>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </template>
    </JFormContainer>
  </a-spin>
</template>

<script lang="ts" setup>
  import { ref, reactive, defineExpose, nextTick, defineProps, computed, onMounted } from 'vue';
  import { defHttp } from '/src/utils/http/axios';
  import { useMessage } from '/src/hooks/web/useMessage';
  import { getValueType } from '/src/utils';
  import { saveOrUpdate } from '../InterfaceTraceLog.api';
  import { Form } from 'ant-design-vue';
  import JFormContainer from '/src/components/Form/src/container/JFormContainer.vue';
  const props = defineProps({
    formDisabled: { type: Boolean, default: false },
    formData: { type: Object, default: () => ({}) },
    formBpm: { type: Boolean, default: true },
  });
  const formRef = ref();
  const useForm = Form.useForm;
  const emit = defineEmits(['register', 'ok']);
  const formData = reactive<Record<string, any>>({
    id: '',
    examNo: '',
    name: '',
    type: '',
    createTime: '',
    okFlag: '',
    description: '',
    costTime: null,
    reqBody: '',
    respBody: '',
    errorMsg: '',
    respStatus: '',
    respTime: null,
    handleBy: '',
    handleTime: '',
    handleResult: '',
  });
  const { createMessage } = useMessage();
  const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 5 } });
  const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 16 } });
  const confirmLoading = ref<boolean>(false);
  //表单验证
  const validatorRules = reactive({});
  const { resetFields, validate, validateInfos } = useForm(formData, validatorRules, { immediate: false });

  // 表单禁用
  const disabled = computed(() => {
    if (props.formBpm === true) {
      if (props.formData.disabled === false) {
        return false;
      } else {
        return true;
      }
    }
    return props.formDisabled;
  });

  /**
   * 新增
   */
  function add() {
    edit({});
  }

  /**
   * 编辑
   */
  function edit(record) {
    nextTick(() => {
      resetFields();
      const tmpData = {};
      Object.keys(formData).forEach((key) => {
        if (record.hasOwnProperty(key)) {
          tmpData[key] = record[key];
        }
      });
      //赋值
      Object.assign(formData, tmpData);
    });
  }

  /**
   * 提交数据
   */
  async function submitForm() {
    try {
      // 触发表单验证
      await validate();
    } catch ({ errorFields }) {
      if (errorFields) {
        const firstField = errorFields[0];
        if (firstField) {
          formRef.value.scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
        }
      }
      return Promise.reject(errorFields);
    }
    confirmLoading.value = true;
    const isUpdate = ref<boolean>(false);
    //时间格式化
    let model = formData;
    if (model.id) {
      isUpdate.value = true;
    }
    //循环数据
    for (let data in model) {
      //如果该数据是数组并且是字符串类型
      if (model[data] instanceof Array) {
        let valueType = getValueType(formRef.value.getProps, data);
        //如果是字符串类型的需要变成以逗号分割的字符串
        if (valueType === 'string') {
          model[data] = model[data].join(',');
        }
      }
    }
    await saveOrUpdate(model, isUpdate.value)
      .then((res) => {
        if (res.success) {
          createMessage.success(res.message);
          emit('ok');
        } else {
          createMessage.warning(res.message);
        }
      })
      .finally(() => {
        confirmLoading.value = false;
      });
  }

  defineExpose({
    add,
    edit,
    submitForm,
  });
</script>

<style lang="less" scoped>
  .antd-modal-form {
    padding: 14px;
  }
</style>
