<template>
  <div>
    <!--查询区域-->
    <div class="jeecg-basic-table-form-container">
      <a-form ref="formRef" @keyup.enter="searchQuery" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-row :gutter="[8, 8]">
          <a-col :span="12">
            <a-input allow-clear size="middle" placeholder="请输入体检号" v-model:value="queryParam.examNo" />
          </a-col>
          <a-col :span="12">
            <j-dict-select-tag
              dict-code="interfaceSyncStatus"
              size="middle"
              placeholder="请选择状态"
              v-model:value="queryParam.interfaceSyncStatus"
              @change="searchQuery"
              style="width: 100%"
            />
          </a-col>
          <a-col :span="12">
            <a-input allow-clear @focus="readIdcard" size="middle" placeholder="请输入身份证" v-model:value="queryParam.idCard" />
          </a-col>
          <a-col :span="12">
            <a-input allow-clear size="middle" placeholder="请输入姓名" v-model:value="queryParam.name" />
          </a-col>
          <a-col :span="24">
            <a-range-picker v-model:value="regDateRange" style="width: 100%" @change="searchQuery" :presets="rangePresets" :allow-clear="false" />
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :span="12">
              <a-input allow-clear size="middle" placeholder="请输入体检卡号" v-model:value="queryParam.examCardNo" />
            </a-col>
            <a-col :span="12">
              <j-dict-select-tag dict-code="sex" size="middle" placeholder="请选择性别" v-model:value="queryParam.gender" />
            </a-col>
            <a-col :span="12">
              <j-async-search-select
                :async="true"
                size="middle"
                placeholder="所属预约"
                @change="getTeamList"
                v-model:value="queryParam.companyRegId"
                dict="company_reg,reg_name,id"
              />
            </a-col>
            <a-col :span="12">
              <a-select size="middle" placeholder="所属分组" v-model:value="queryParam.teamId">
                <a-select-option value="">请选择</a-select-option>
                <a-select-option :value="item.id" v-for="item in teamList">{{ item.name }}</a-select-option>
              </a-select>
            </a-col>
            <!--            <a-col :span="12">
              <a-select size="middle" placeholder="修改申请状态" v-model:value="queryParam.changeApplyStatus">
                <a-select-option value="">请选择</a-select-option>
                <a-select-option :value="item" v-for="item in ['待确认', '已同意', '已拒绝', '已完成']">{{ item }}</a-select-option>
              </a-select>
            </a-col>-->
            <!--          <a-row :gutter="12">
            <a-col :span="12">
              <a-form-item name="itemGroupId">
                <j-async-search-select
                  :async="true"
                  size="middle"
                  placeholder="套餐"
                  @change="getItemList"
                  v-model:value="queryParam.itemGroupId"
                  dict="item_group,name,id"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item name="itemId">
                <a-select size="middle" placeholder="项目" v-model:value="queryParam.itemId">
                  <a-select-option value="">请选择</a-select-option>
                  <a-select-option :value="item.id" v-for="item in itemList">{{ item.name }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>-->
          </template>
          <a-col :xl="12" :span="12">
            <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
              <a-button size="middle" type="primary" preIcon="ant-design:search-outlined" @click="searchQuery">查询</a-button>
              <a-button size="middle" type="primary" preIcon="ant-design:reload-outlined" @click="searchReset" style="margin-left: 8px"
                >重置</a-button
              >
              <a @click="toggleSearchStatus = !toggleSearchStatus" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <Icon :icon="toggleSearchStatus ? 'ant-design:up-outlined' : 'ant-design:down-outlined'" />
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="null">
      <!--插槽:table标题-->
      <template #bodyCell="{ column, text, record, index }">
        <template v-if="column.dataIndex == 'name'">
          {{ text }}
        </template>
        <template v-if="column.dataIndex == 'interfaceStatusStatList'">
          <a-space
            ><a-tag :bordered="false" :color="status.color" v-for="status in record.interfaceStatusStatList">
              {{ status.status }} {{ status.count }}</a-tag
            ></a-space
          >
        </template>
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" name="customer-reg-list4-interface-check" setup>
  import { computed, inject, reactive, ref, toRaw, watch } from 'vue';
  import type { RangeValue } from '#/types';
  import { BasicTable } from '/src/components/Table';
  import { useListPage } from '/src/hooks/system/useListPage';
  import { columns4InterfaceCheck, columns4Station } from '/src/views/reg/CustomerReg.data';
  import { getItemByGroupId } from '@/views/basicinfo/ItemGroup.api';
  import { getRegById, listReg } from '/src/views/station/Station.api';
  import { companyTeamList } from '/src/views/reg/CompanyReg.api';
  import { useUserStore } from '/src/store/modules/user';
  import JDictSelectTag from '@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import { theme } from 'ant-design-vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { EyeTwoTone } from '@ant-design/icons-vue';
  import { JAsyncSearchSelect } from '@/components/Form';
  import dayjs from 'dayjs';
  import { IdcData } from '#/utils';
  import { listReg4Interface } from '@/views/interfacecheck/RegInterfaceCheck.api';

  const { createConfirm, notification } = useMessage();
  const emit = defineEmits(['rowClick', 'readIdcard', 'add', 'batchRegOk']);
  const formRef = ref();
  const queryParam = reactive<any>({ checkState: '', ignoreDepartment: '1' });
  const toggleSearchStatus = ref<boolean>(false);
  const userStore = useUserStore();
  const { token } = theme.useToken();

  /*处理身份证读取*/
  const idcData = inject<IdcData>('idCardDataKey', {
    data: {},
    ok: false,
    msg: '',
    state: '',
    action: '',
  });
  const sendIdCardCmd = inject('idCardSendMethod', (cmd) => {});
  const idcDataSource = computed(() => ({
    data: idcData.data,
    action: idcData.action,
  }));
  watch(idcDataSource, (val) => {
    if (val.data.idCardNo && val.action == 'searchStationRegList') {
      queryParam.idCard = toRaw(val.data.idCardNo);
      searchQuery();
    }
  });
  function readIdcard() {
    queryParam.idCard = null;
    idcData.data = {};
    idcData.action = 'searchStationRegList';
    sendIdCardCmd('ReadStart');
  }

  const props = defineProps({
    departments: { type: String, default: () => '' },
  });
  const rangePresets = ref([
    { label: '今天', value: [dayjs(), dayjs()] },
    { label: '过去一周', value: [dayjs().add(-7, 'd'), dayjs()] },
    { label: '过去二周', value: [dayjs().add(-14, 'd'), dayjs()] },
    { label: '过去30天', value: [dayjs().add(-30, 'd'), dayjs()] },
    { label: '过去90天', value: [dayjs().add(-90, 'd'), dayjs()] },
    { label: '过去一年', value: [dayjs().add(-1, 'y'), dayjs()] },
    { label: '过去两年', value: [dayjs().add(-2, 'y'), dayjs()] },
    { label: '过去二十年', value: [dayjs().add(-20, 'y'), dayjs()] },
  ]);

  const regDateRange = ref<RangeValue>([dayjs(), dayjs()]);

  /**表格相关操作*/
  const currentRow = ref<any>({});
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      showTableSetting: false,
      showIndexColumn: true,
      api: listReg4Interface,
      columns: columns4InterfaceCheck,
      canResize: true,
      canColDrag: true,
      useSearchForm: false,
      clickToRowSelect: false,
      size: 'small',
      striped: true,
      actionColumn: {
        ifShow: false,
      },
      customRow: (record) => {
        return {
          onClick: () => {
            currentRow.value = record;
            emit('rowClick', record);
          },
        };
      },
      beforeFetch: (params) => {
        if (regDateRange.value && regDateRange.value.length == 2) {
          queryParam.regDateStart = regDateRange.value[0].format('YYYY-MM-DD') + ' 00:00:00';
          queryParam.regDateEnd = regDateRange.value[1].format('YYYY-MM-DD') + ' 23:59:59';
        }
        return Object.assign(params, queryParam);
      },
      afterFetch: (dataSource) => {
        if (currentRow.value && dataSource.length > 0) {
          let record = dataSource.find((item) => item.id === currentRow.value.id);
          if (record) {
            currentRow.value = record;
            emit('rowClick', record);
          }
        }
        return dataSource;
      },
      rowClassName: (record) => {
        return currentRow.value && currentRow.value.id === record.id ? 'row-selected' : '';
      },
    },
  });
  const [registerTable, { reload, collapseAll, updateTableDataRecord, findTableDataRecord, getDataSource }] = tableContext;

  const itemList = ref<any[]>([]);
  const teamList = ref<any[]>([]);
  const labelCol = reactive({
    xs: 0,
    sm: 0,
    xl: 0,
    xxl: 0,
  });
  const wrapperCol = reactive({
    xs: 24,
    sm: 24,
    xl: 24,
    xxl: 24,
  });

  function getTeamList(companyRegId) {
    if (!companyRegId) {
      teamList.value = [];
      queryParam.teamId = '';
      return;
    }
    teamList.value = [];
    queryParam.teamId = '';
    companyTeamList({ companyRegId: companyRegId, pageSize: 10000 }).then((res) => {
      teamList.value = res.records;
    });
  }

  function getItemList(itemGroupId) {
    if (!itemGroupId) {
      itemList.value = [];
      queryParam.itemId = '';
      return;
    }
    itemList.value = [];
    queryParam.itemId = '';

    getItemByGroupId({ groupId: itemGroupId }).then((res) => {
      itemList.value = res;
    });
  }

  /**
   * 查询
   */
  async function searchQuery() {
    if (regDateRange.value) {
      queryParam.regDateStart = regDateRange.value[0].format('YYYY-MM-DD');
      queryParam.regDateEnd = regDateRange.value[1].format('YYYY-MM-DD');
    }
    await reload();
    //如果有多条数据，自动选中第一条，并发送selectChange
    let dataSource = getDataSource();
    if (dataSource.length > 0) {
      currentRow.value = dataSource[0];
      emit('rowClick', dataSource[0]);
    } else {
      emit('rowClick', {});
    }
  }

  /**
   * 重置
   */
  function searchReset() {
    formRef.value.resetFields();
    //刷新数据
    reload();
  }

  function reloadPage() {
    reload();
  }

  function reloadCurrentRow() {
    if (currentRow.value.id) {
      getRegById({ regId: currentRow.value.id, departmentId: props.departments }).then((res) => {
        if (res) {
          Object.assign(currentRow.value, res);
          emit('rowClick', res);
        }
      });
    }
  }

  watch(
    () => props.departments,
    (value) => {
      if (value) {
        queryParam.departmentId = value;
        searchQuery();
      } else {
        queryParam.departmentId = '';
        searchQuery();
      }
    }
  );

  defineExpose({
    searchQuery,
    reloadPage,
    reloadCurrentRow,
  });
</script>

<style lang="less" scoped>
  .jeecg-basic-table-form-container {
    padding: 0;
    .table-page-search-submitButtons {
      display: block;
      margin-bottom: 12px;
      white-space: nowrap;
    }
    .query-group-cust {
      min-width: 100px !important;
    }
    .query-group-split-cust {
      width: 30px;
      display: inline-block;
      text-align: center;
    }
  }

  :deep(.row-selected td:first-child) {
    border-left: solid 5px v-bind('token.colorPrimary');
  }
</style>
