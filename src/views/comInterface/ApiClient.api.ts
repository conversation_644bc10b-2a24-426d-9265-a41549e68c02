import { defHttp } from '/@/utils/http/axios';
import { useMessage } from "/@/hooks/web/useMessage";

const { createConfirm } = useMessage();

enum Api {
  list = '/comInterface/apiClient/list',
  save='/comInterface/apiClient/add',
  edit='/comInterface/apiClient/edit',
  deleteOne = '/comInterface/apiClient/delete',
  deleteBatch = '/comInterface/apiClient/deleteBatch',
  importExcel = '/comInterface/apiClient/importExcel',
  exportXls = '/comInterface/apiClient/exportXls',
  generateKeys = '/comInterface/apiClient/generateKeys',
  generateKeysByType = '/comInterface/apiClient/generateKeysByType',
  generateCustomKeys = '/comInterface/apiClient/generateCustomKeys',
  validateKeyStrength = '/comInterface/apiClient/validateKeyStrength',
}

/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;

/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params,handleSuccess) => {
  return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}

/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({url: Api.deleteBatch, data: params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}

/**
 * 保存或者更新
 * @param params
 * @param isUpdate
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params }, { isTransformResponse: false });
}

/**
 * 一键生成API密钥
 */
export const generateKeys = () => {
  return defHttp.post({ url: Api.generateKeys }, { isTransformResponse: false });
}

/**
 * 生成指定类型的API密钥对
 * @param type 密钥类型：STANDARD, SECURE, HEX, BASE64
 */
export const generateKeysByType = (type = 'SECURE') => {
  return defHttp.post({ url: `${Api.generateKeysByType}?type=${encodeURIComponent(type)}` }, { isTransformResponse: false });
}

/**
 * 生成自定义长度的API密钥
 * @param keyLength API密钥长度
 * @param secretLength API秘钥长度
 * @param includeSpecialChars 是否包含特殊字符
 */
export const generateCustomKeys = (keyLength = 32, secretLength = 64, includeSpecialChars = false) => {
  const params = new URLSearchParams({
    keyLength: keyLength.toString(),
    secretLength: secretLength.toString(),
    includeSpecialChars: includeSpecialChars.toString()
  });
  return defHttp.post({ url: `${Api.generateCustomKeys}?${params.toString()}` }, { isTransformResponse: false });
}

/**
 * 验证API密钥强度
 * @param apiKey API密钥
 * @param apiSecret API秘钥
 */
export const validateKeyStrength = (apiKey, apiSecret) => {
  return defHttp.post({
    url: `${Api.validateKeyStrength}?apiKey=${encodeURIComponent(apiKey)}&apiSecret=${encodeURIComponent(apiSecret)}`
  }, { isTransformResponse: false });
}
