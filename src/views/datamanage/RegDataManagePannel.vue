<template>
  <div style="padding: 5px">
    <a-row :gutter="4">
      <a-col :span="6">
        <a-card size="small" title="体检人员列表" style="padding: 0; min-height: 90vh">
          <customer-reg-list4-data-manage ref="customerRegList" @row-click="fetchDepartList" />
        </a-card>
      </a-col>
      <a-col :span="18" style="background-color: #ffffff">
        <a-tabs v-model:activeKey="activeTabKey" @change="handleTabChange">
          <a-tab-pane key="interfaceStatus" tab="项目状态">
            <a-tabs v-model:activeKey="currentDepartmentId" @change="handleDepartTabChange" style="margin-top: 5px">
              <a-tab-pane :key="item.departmentId" v-for="(item, index) in departStatList">
                <template #tab>
                  <span>{{ item.departmentName }}</span>
                </template>
              </a-tab-pane>
            </a-tabs>
            <RegItemGroupList4DataMange ref="regItemGroupList" @success="reloadCustomerRegList" />
          </a-tab-pane>
          <!--          <a-tab-pane key="interfaceLog" tab="接口记录">
            <InterfaceTraceLogList4Reg ref="interfaceLogListRef"  />
          </a-tab-pane>-->
        </a-tabs>
      </a-col>
    </a-row>
  </div>
</template>

<script lang="ts" setup name="regDataMangePannel">
  import { computed, nextTick, ref, watch, provide } from 'vue';
  import { DepartStat, ICustomerReg } from '#/types';
  import { useMessage } from '@/hooks/web/useMessage';
  import { regGroupsGroupByDepart } from '@/views/datamanage/RegDataManage.api';
  import RegItemGroupList4DataMange from '@/views/datamanage/RegItemGroupList4DataManage.vue';
  import CustomerRegList4DataManage from '@/views/datamanage/CustomerRegList4DataManage.vue';

  const { createConfirm, createErrorModal } = useMessage();
  const loading = ref(false);

  const departStatList = ref<DepartStat[]>([]);
  const activeTabKey = ref('interfaceStatus');
  const currentDepartmentId = ref('');
  const currentDepart = computed(() => {
    return departStatList.value.find((item) => item.departmentId === currentDepartmentId.value)?.depart;
  });
  const customerRegList = ref(null);
  const currentReg = ref<ICustomerReg>({});

  const regItemGroupList = ref(null);
  const interfaceLogListRef = ref(null);
  const name = ref('');
  // 提供数据
  provide('customerName', name.value);
  function reloadCustomerRegList() {
    customerRegList.value?.reloadCurrentRow();
  }

  function handleTabChange(key) {
    activeTabKey.value = key;
    if (key === 'interfaceStatus') {
      handleDepartTabChange(currentDepartmentId.value);
    } else if (key === 'interfaceLog') {
      interfaceLogListRef.value?.loadData(currentReg.value);
    }
  }

  function handleDepartTabChange(key) {
    currentDepartmentId.value = key;
    regItemGroupList.value?.loadData(currentReg.value, currentDepart.value);
  }

  function fetchDepartList(selectedCustomerReg: ICustomerReg) {
    activeTabKey.value = 'interfaceStatus';
    currentReg.value = selectedCustomerReg;
    name.value = selectedCustomerReg.name;
    let reqParam = { customerRegId: currentReg.value.id };
    loading.value = true;
    regGroupsGroupByDepart(reqParam)
      .then((res) => {
        departStatList.value = res;
        if (res.length > 0) {
          nextTick(() => {
            currentDepartmentId.value = res[0].departmentId;
            regItemGroupList.value?.loadData(currentReg.value, currentDepart.value);
          });
        }
      })
      .finally(() => {
        loading.value = false;
      });
  }
</script>
<style scoped>
  .abandon {
    text-decoration: line-through #ff4d4f;
  }

  .active-border {
    animation: glow 800ms ease-out infinite alternate;
  }

  @keyframes glow {
    0% {
      border-color: #0a8fe9;
      box-shadow: 0 0 5px rgba(10, 143, 233, 0.2);
    }
    100% {
      border-color: #0a8fe9;
      box-shadow: 0 0 20px rgba(10, 143, 233, 0.6);
    }
  }

  .error-modal {
    max-height: 50vh;
    overflow-y: scroll;
  }
  .done {
    border-left: #00db00 solid 2px;
  }
  .wait {
    border-left: #ff4d4f solid 2px;
  }
</style>
