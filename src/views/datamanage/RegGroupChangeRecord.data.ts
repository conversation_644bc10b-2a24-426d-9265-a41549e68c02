import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '体检号',
    align: "center",
    dataIndex: 'examNo'
  },
  {
    title: '检客姓名',
    align: "center",
    dataIndex: 'customerName'
  },
  {
    title: '项目名称',
    align: "center",
    dataIndex: 'regGroupName'
  },
  {
    title: '检查部位',
    align: "center",
    dataIndex: 'checkPartName'
  },
  {
    title: '变更原因',
    align: "center",
    dataIndex: 'reason'
  },

  {
    title: '变更前检查状态',
    align: "center",
    dataIndex: 'oldCheckStatus'
  },
  {
    title: '变更后检查状态',
    align: "center",
    dataIndex: 'newCheckStatus'
  },
  {
    title: '变更前接口状态',
    align: "center",
    dataIndex: 'oldInferfaceStatus'
  },
  {
    title: '变更后接口状态',
    align: "center",
    dataIndex: 'newInterfaceStatus'
  },
  {
    title: '创建时间',
    align: "center",
    dataIndex: 'createTime'
  },
  {
    title: '操作人',
    align: "center",
    dataIndex: 'createBy_dictText'
  },
];

// 高级查询数据
export const superQuerySchema = {
  examNo: {title: '体检号',order: 0,view: 'text', type: 'string',},
  customerName: {title: '检客姓名',order: 1,view: 'text', type: 'string',},
  regGroupName: {title: '项目名称',order: 2,view: 'text', type: 'string',},
  reason: {title: '变更原因',order: 3,view: 'text', type: 'string',},
  createTime: {title: '创建时间',order: 4,view: 'datetime', type: 'string',},
  createBy: {title: '操作人',order: 5,view: 'text', type: 'string',},
  oldCheckStatus: {title: '变更前检查状态',order: 6,view: 'text', type: 'string',},
  newCheckStatus: {title: '变更后检查状态',order: 7,view: 'text', type: 'string',},
  oldInferfaceStatus: {title: '变更前接口状态',order: 8,view: 'text', type: 'string',},
  newInterfaceStatus: {title: '变更后接口状态',order: 9,view: 'text', type: 'string',},
};
