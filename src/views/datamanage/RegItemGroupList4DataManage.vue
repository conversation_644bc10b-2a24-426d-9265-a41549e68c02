<template>
  <div class="p-2" v-if="queryParam.regId">
    <!--查询区域-->
    <div class="jeecg-basic-table-form-container">
      <a-form ref="formRef" @keyup.enter.native="searchQuery" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-row :gutter="24"> </a-row>
      </a-form>
    </div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <!--        <a-button type="primary" v-auth="'customer_reg_item_group:add'"  @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
        <a-button  type="primary" v-auth="'customer_reg_item_group:exportXls'" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button>
        <j-upload-button  type="primary" v-auth="'customer_reg_item_group:importExcel'"  preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button>
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined"></Icon>
                删除
              </a-menu-item>
            </a-menu>
          </template>
          <a-button v-auth="'customer_reg_item_group:deleteBatch'">批量操作
            <Icon icon="mdi:chevron-down"></Icon>
          </a-button>
        </a-dropdown>-->
        <!-- 高级查询 -->
        <!--        <super-query :config="superQueryConfig" @search="handleSuperQuery" />-->
        <a-button v-if="selectedRowKeys.length > 0" type="primary" @click="handleCopyResults" preIcon="ant-design:plus-outlined">
          批量同步结果</a-button
        >
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
      </template>
      <template v-slot:bodyCell="{ column, record, index, text }"> </template>
    </BasicTable>
    <!-- 表单区域 -->
    <CustomerRegItemGroupModal ref="registerModal" @success="handleSuccess"></CustomerRegItemGroupModal>
    <CopySourceGroupListModal ref="sourceGroupListModal" @success="handleSuccess"></CopySourceGroupListModal>
    <RegResultAddModal ref="regResultAddModal" @success="handleSuccess"></RegResultAddModal>
  </div>
</template>

<script lang="ts" name="data-manage-customerRegItemGroup" setup>
  import { ref, reactive } from 'vue';
  import { BasicTable, useTable, TableAction } from '/src/components/Table';
  import { useListPage } from '/src/hooks/system/useListPage';
  import { columns, superQuerySchema } from '../station/CustomerRegItemGroup.data';
  import CustomerRegItemGroupModal from './components/CustomerRegItemGroupModal.vue';
  import CopySourceGroupListModal from './components/CopySourceGroupListModal.vue';
  import { useUserStore } from '/src/store/modules/user';
  import { getGroupsByRegIdAndDepartId } from '@/views/datamanage/RegDataManage.api.ts';
  import { Depart, ICustomerReg } from '#/types';
  import { message } from 'ant-design-vue';
  import RegResultAddModal from '@/views/datamanage/components/RegResultAddModal.vue';

  const formRef = ref();
  const queryParam = reactive<any>({});
  const currentReg = ref();
  const toggleSearchStatus = ref<boolean>(false);
  const registerModal = ref();
  const sourceGroupListModal = ref();
  const regResultAddModal = ref();
  const userStore = useUserStore();
  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: '项目组合列表',
      api: getGroupsByRegIdAndDepartId,
      columns,
      canResize: false,
      useSearchForm: false,
      actionColumn: {
        width: 120,
        fixed: 'right',
      },
      beforeFetch: async (params) => {
        return Object.assign(params, queryParam);
      },
    },
    /*  exportConfig: {
      name: "customer_reg_item_group",
      url: getExportUrl,
      params: queryParam,
    },
	  importConfig: {
	    url: getImportUrl,
	    success: handleSuccess
	  },*/
  });
  const [
    registerTable,
    { reload, collapseAll, updateTableDataRecord, findTableDataRecord, getDataSource },
    { rowSelection, selectedRowKeys, selectedRows },
  ] = tableContext;
  const labelCol = reactive({
    xs: 24,
    sm: 4,
    xl: 6,
    xxl: 4,
  });
  const wrapperCol = reactive({
    xs: 24,
    sm: 20,
  });

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  async function loadData(reg: ICustomerReg, depart: Depart) {
    if (reg?.id && depart?.id) {
      queryParam.departmentId = depart.id;
      queryParam.regId = reg.id;
      currentReg.value = reg;
      //刷新数据
      reload();
      selectedRowKeys.value = [];
      selectedRows.value = [];
    }
  }
  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    searchQuery();
  }

  /**
   * 批量同步结果
   */
  function handleCopyResults() {
    let dataSource = getDataSource();
    let checkedGroupList = dataSource.filter((item) => item.checkStatus != '未检' && item.classCode == '检查') || [];
    if (checkedGroupList.length == 0) {
      message.error('没有可以用来同步的结果数据！');
      return;
    }

    // 检查是否有选中的行
    if (selectedRowKeys.value.length === 0) {
      message.error('请先选择需要进行同步的项目！');
      return;
    }

    // 检查选中的项目中是否有非检查类项目（检验项目）
    let nonCheckItems = selectedRows.value.filter((item) => item.classCode != '检查');
    if (nonCheckItems.length > 0) {
      message.error('选择的项目中存在检验项目，无法同步结果！');
      return;
    }
    // 检查选中的项目中是否有已检项目
    let checkedItems = selectedRows.value.filter((item) => item.checkStatus != '未检');
    if (checkedItems.length > 0) {
      message.error('选择的项目中存在已检项目，无法同步结果！');
      return;
    }
    sourceGroupListModal?.value?.open(selectedRowKeys.value, checkedGroupList);
  }

  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    registerModal.value.disableSubmit = false;
    registerModal.value.edit(record);
  }

  /**
   * 详情
   */

  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }

  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        auth: 'datamanage:customer_reg_item_group:edit',
      },
    ];
  }

  /**
   * 下拉操作栏
   */
  function getDropDownAction(record) {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      },
      {
        label: '同步结果',
        disabled: record.checkStatus != '未检' || record.classCode != '检查',
        onClick: () => {
          let dataSource = getDataSource();
          let checkedGroupList = dataSource.filter((item) => item.checkStatus == '已检' && item.classCode == '检查') || [];
          if (checkedGroupList.length == 0) {
            message.error('没有可以用来同步的结果数据！');
            return;
          }
          sourceGroupListModal?.value?.open([record.id], checkedGroupList);
        },
      },
      {
        label: '结果录入',
        disabled: record.checkStatus != '未检' || record.classCode == '检验',
        onClick: () => {
          record.archivesNum = currentReg.value.archivesNum;
          record.idCard = currentReg.value.idCard;
          record.customerRegId = currentReg.value.id;
          regResultAddModal?.value?.open(record);
        },
      },
    ];
  }
  async function handleDetail(record: Recordable) {
    registerModal.value.disableSubmit = true;
    registerModal.value.edit(record);
  }

  /**
   * 查询
   */
  function searchQuery() {
    reload();
  }

  /**
   * 重置
   */
  function searchReset() {
    formRef.value.resetFields();
    selectedRowKeys.value = [];
    //刷新数据
    reload();
  }

  defineExpose({
    loadData,
  });
</script>

<style lang="less" scoped>
  .jeecg-basic-table-form-container {
    padding: 0;
    .table-page-search-submitButtons {
      display: block;
      margin-bottom: 24px;
      white-space: nowrap;
    }
    .query-group-cust {
      min-width: 100px !important;
    }
    .query-group-split-cust {
      width: 30px;
      display: inline-block;
      text-align: center;
    }
    .ant-form-item:not(.ant-form-item-with-help) {
      margin-bottom: 16px;
      height: 32px;
    }
    :deep(.ant-picker),
    :deep(.ant-input-number) {
      width: 100%;
    }
  }
</style>
