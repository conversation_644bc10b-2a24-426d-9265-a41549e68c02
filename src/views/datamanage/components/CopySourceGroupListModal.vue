<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭"
    okText="同步"
    :okButtonProps="{
      type: 'primary',
      loading: loading,
      class: { 'jee-hidden': disableSubmit },
    }"
    :cancelButtonProps="{ type: 'default' }"
  >
    <!-- 弹窗头部说明 -->
    <div class="modal-header-info">
      <a-alert message="请选择一个源组合项目进行结果同步" type="info" show-icon style="margin-bottom: 16px" />
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <a-table
        :row-selection="rowSelection"
        :columns="columns"
        :data-source="dataSource"
        :row-key="(record) => record.id"
        :pagination="{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条数据`,
        }"
        size="middle"
        :scroll="{ y: 350 }"
      >
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'itemGroupName'">
            <div class="item-group-name">
              <a-tooltip :title="text">
                <span class="group-name-text">{{ text }}</span>
              </a-tooltip>
            </div>
          </template>
          <template v-if="column.dataIndex === 'hisCode'">
            <a-tag color="blue">{{ text }}</a-tag>
          </template>
          <template v-if="column.dataIndex === 'checkStatus'">
            <a-tag :color="getStatusColor(text)">
              {{ text }}
            </a-tag>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 底部统计信息 -->
    <div class="modal-footer-info" v-if="dataSource.length > 0">
      <a-divider style="margin: 16px 0 12px 0" />
      <div class="footer-stats">
        <a-space size="large">
          <div class="stat-item">
            <span class="stat-label">可选项目：</span>
            <a-tag color="success" class="stat-tag">{{ dataSource.length }}</a-tag>
          </div>
          <div class="stat-item" v-if="selectedRows.length > 0">
            <span class="stat-label">已选择：</span>
            <a-tag color="processing" class="stat-tag">{{ selectedRows.length }}</a-tag>
          </div>
        </a-space>
      </div>
    </div>
  </j-modal>
</template>

<script lang="ts" setup>
  import { ref, nextTick, defineExpose, computed, toRaw } from 'vue';
  import JModal from '/src/components/Modal/src/JModal/JModal.vue';
  import { copyResult } from '@/views/datamanage/RegDataManage.api';
  import { message } from 'ant-design-vue';

  const title = ref<string>('结果同步');
  const width = ref<number>(900);
  const visible = ref<boolean>(false);
  const disableSubmit = ref<boolean>(false);
  const loading = ref<boolean>(false);
  const registerForm = ref();
  const emit = defineEmits(['register', 'success']);
  const dataSource = ref<any[]>([]);
  const selectedRowKeys = ref<string[]>([]);
  const selectedRows = ref<any[]>([]);
  let toGroupIdList = [];

  const columns = [
    {
      title: '组合名称',
      align: 'center',
      dataIndex: 'itemGroupName',
    },
    {
      title: '组合编码',
      align: 'center',
      dataIndex: 'hisCode',
    },
    {
      title: '检查状态',
      align: 'center',
      dataIndex: 'checkStatus',
    },
  ];

  // 单选配置
  const rowSelection = computed(() => ({
    type: 'radio', // 单选模式
    selectedRowKeys: selectedRowKeys.value,
    onChange: (keys: string[], rows: any[]) => {
      // console.log('选择变化:', keys, rows);
      selectedRowKeys.value = keys;
      selectedRows.value = rows;
    },
  }));

  // 获取状态颜色
  function getStatusColor(status: string) {
    switch (status) {
      case '已检':
        return 'success';
      case '未检':
        return 'default';
      case '检查中':
        return 'processing';
      default:
        return 'default';
    }
  }

  /**
   * 新增
   */
  function open(groupIdList, checkedGroupList) {
    visible.value = true;
    // 清空之前的选择
    selectedRowKeys.value = [];
    selectedRows.value = [];
    dataSource.value = checkedGroupList;
    toGroupIdList = groupIdList;
  }

  /**
   * 确定按钮点击事件
   */
  function handleOk() {
    if (selectedRows.value.length === 0) {
      message.warning('请选择一个源组合项目');
      return;
    }

    // 获取选中的项目
    const fromGroup = selectedRows.value[0];

    loading.value = true;

    copyResult({ toGroupIdList: toGroupIdList, fromGroup: fromGroup })
      .then((res) => {
        if (res.code === 200) {
          message.success('结果同步成功');
          emit('success', fromGroup);
          handleCancel();
        } else {
          message.error(res.message || '同步失败');
        }
      })
      .catch((error) => {
        console.error('同步失败:', error);
        message.error('同步失败，请重试');
      })
      .finally(() => {
        loading.value = false;
      });
  }

  /**
   * form保存回调事件
   */
  function submitCallback() {
    handleCancel();
    emit('success');
  }

  /**
   * 取消按钮回调事件
   */
  function handleCancel() {
    visible.value = false;
    // 清空选择状态
    selectedRowKeys.value = [];
    selectedRows.value = [];
  }

  defineExpose({
    open,
  });
</script>

<style lang="less">
  /**隐藏样式-modal确定按钮 */
  .jee-hidden {
    display: none !important;
  }
</style>

<style lang="less" scoped>
  .modal-header-info {
    margin-bottom: 16px;
  }

  .table-container {
    .item-group-name {
      .group-name-text {
        font-weight: 500;
        //color: #1890ff;
        cursor: pointer;

        &:hover {
          //color: #40a9ff;
        }
      }
    }
  }

  .modal-footer-info {
    .footer-stats {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 12px 16px;
      background-color: #fafafa;
      border-radius: 6px;

      .stat-item {
        display: flex;
        align-items: center;

        .stat-label {
          font-size: 14px;
          color: #666;
          margin-right: 8px;
        }

        .stat-tag {
          font-size: 14px;
          font-weight: 600;
          padding: 4px 12px;
          border-radius: 12px;
        }
      }
    }
  }

  // 表格样式优化
  :deep(.ant-table) {
    .ant-table-thead > tr > th {
      background-color: #f5f5f5;
      font-weight: 600;
    }

    .ant-table-tbody > tr:hover > td {
      background-color: #e6f7ff;
    }

    .ant-radio-wrapper {
      margin-right: 0;
    }
  }

  // 分页样式
  :deep(.ant-pagination) {
    margin-top: 16px;
    text-align: center;
  }
</style>
