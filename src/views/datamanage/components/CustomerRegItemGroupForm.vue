<template>
  <a-spin :spinning="confirmLoading">
    <JFormContainer :disabled="disabled">
      <template #detail>
        <a-form ref="formRef" class="antd-modal-form" :labelCol="labelCol" :wrapperCol="wrapperCol" name="CustomerRegItemGroupForm">
          <a-row>
            <a-col :span="24">
              <a-form-item label="组合名称" v-bind="validateInfos.itemGroupName" id="CustomerRegItemGroupForm-itemGroupName" name="itemGroupName">
                <a-input v-model:value="formData.itemGroupName" placeholder="请输入组合名称" disabled />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="检查部位" v-bind="validateInfos.checkPartName" id="RegGroupChangeRecordForm-checkPartName" name="checkPartName">
                <a-input v-model:value="formData.checkPartName" placeholder="请输入检查部位" disabled></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="组合类型" v-bind="validateInfos.classCode" id="CustomerRegItemGroupForm-classCode" name="classCode">
                <a-input v-model:value="formData.classCode" placeholder="请输入组合类型" disabled />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="折后价" v-bind="validateInfos.priceAfterDis" id="CustomerRegItemGroupForm-priceAfterDis" name="priceAfterDis">
                <a-input-number v-model:value="formData.priceAfterDis" placeholder="请输入折后价" style="width: 100%" disabled />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="支付方" v-bind="validateInfos.payerType" id="CustomerRegItemGroupForm-payerType" name="payerType">
                <a-input v-model:value="formData.payerType" placeholder="请输入支付方" disabled />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="支付状态" v-bind="validateInfos.payStatus" id="CustomerRegItemGroupForm-payStatus" name="payStatus">
                <j-dict-select-tag v-model:value="formData.payStatus" :dictCode="'payStatus'" placeholder="请输入支付状态" />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="检查状态" v-bind="validateInfos.checkStatus" id="CustomerRegItemGroupForm-checkStatus" name="checkStatus">
                <!--                <a-input v-model:value="formData.checkStatus" placeholder="请输入检查状态" />-->
                <j-dict-select-tag placeholder="请输入检查状态" v-model:value="formData.checkStatus" :dictCode="'checkStatus'" />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item
                label="接口状态"
                v-bind="validateInfos.interfaceSyncStatus"
                id="CustomerRegItemGroupForm-interfaceSyncStatus"
                name="interfaceSyncStatus"
              >
                <j-dict-select-tag placeholder="请输入接口状态" v-model:value="formData.interfaceSyncStatus" :dictCode="'interfaceSyncStatus'" />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="图片同步状态" v-bind="validateInfos.picSyncStatus" id="CustomerRegItemGroupForm-picSyncStatus" name="picSyncStatus">
                <a-input v-model:value="formData.picSyncStatus" placeholder="请输入接口调用时间" />
              </a-form-item>
            </a-col>
            <!-- 只有当状态发生变更时才显示变更原因和变更凭证字段 -->
            <template v-if="hasStatusChanged || formData.changeReason">
              <a-col :span="24">
                <a-form-item label="变更原因" v-bind="validateInfos.changeReason" id="CustomerRegItemGroupForm-changeReason" name="changeReason">
                  <a-textarea :auto-size="{ minRows: 3 }" v-model:value="formData.changeReason" placeholder="请输入变更原因" />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item label="变更凭证" v-bind="validateInfos.changePic" id="CustomerRegItemGroupForm-changePic" name="changePic">
                  <j-image-upload :fileMax="0" v-model:value="formData.changePic" />
                </a-form-item>
              </a-col>
            </template>
          </a-row>
        </a-form>
      </template>
    </JFormContainer>
  </a-spin>
</template>

<script lang="ts" setup>
  import { ref, reactive, defineExpose, nextTick, defineProps, computed, onMounted, watch, inject } from 'vue';
  import { useMessage } from '/src/hooks/web/useMessage';
  import { getValueType } from '/src/utils';

  import { Form } from 'ant-design-vue';
  import JFormContainer from '/src/components/Form/src/container/JFormContainer.vue';
  import { updateRegGroupsStatus } from '@/views/datamanage/RegDataManage.api';
  import JDictSelectTag from '@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import JImageUpload from '../../../components/Form/src/jeecg/components/JImageUpload.vue';
  const props = defineProps({
    formDisabled: { type: Boolean, default: false },
    formData: { type: Object, default: () => ({}) },
    formBpm: { type: Boolean, default: true },
  });
  const formRef = ref();
  const useForm = Form.useForm;
  const emit = defineEmits(['register', 'ok']);
  // 注入数据
  const customerName = inject('customerName');

  // 用于存储表单的初始数据
  const initialFormData = ref({});

  // 需要监控的状态字段
  const statusFields = ['payStatus', 'checkStatus', 'interfaceSyncStatus', 'picSyncStatus'];

  const formData = reactive<Record<string, any>>({
    id: '',
    name: customerName,
    customerRegId: '',
    itemGroupId: '',
    itemGroupName: '',
    departmentId: '',
    departmentName: '',
    addMinusFlag: undefined,
    price: undefined,
    disRate: undefined,
    priceAfterDis: undefined,
    payerType: '',
    payStatus: '',
    checkTime: '',
    checkStatus: '',
    classCode: '',
    interfaceSyncStatus: '',
    createTime: '',
    interfaceText: '',
    picSyncStatus: '',
    changeReason: '',
    changePic: '',
    checkPartName: null,
    checkPartCode: null,
  });
  const { createMessage } = useMessage();
  const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 5 } });
  const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 16 } });
  const confirmLoading = ref<boolean>(false);

  // 判断状态是否发生变更
  const hasStatusChanged = computed(() => {
    if (!initialFormData.value || Object.keys(initialFormData.value).length === 0) {
      return false;
    }

    return statusFields.some((field) => {
      const currentValue = formData[field];
      const initialValue = initialFormData.value[field];
      return currentValue !== initialValue;
    });
  });

  // 动态验证规则
  const validatorRules = computed(() => {
    const rules = {
      customerRegId: [{ required: true, message: '请输入登记ID!' }],
      itemGroupId: [{ required: true, message: '请输入组合ID!' }],
      departmentId: [{ required: true, message: '请输入科室ID!' }],
    };

    // 只有当状态发生变更时，变更原因才必填
    if (hasStatusChanged.value) {
      rules.changeReason = [
        {
          required: true,
          message: '请输入变更原因!',
        },
      ];
    }

    return rules;
  });

  const { resetFields, validate, validateInfos } = useForm(formData, validatorRules, { immediate: false });

  // 表单禁用
  const disabled = computed(() => {
    if (props.formBpm === true) {
      if (props.formData.disabled === false) {
        return false;
      } else {
        return true;
      }
    }
    return props.formDisabled;
  });

  /**
   * 新增
   */
  function add() {
    edit({});
  }

  /**
   * 编辑
   */
  function edit(record) {
    nextTick(() => {
      resetFields();
      const tmpData = {};
      Object.keys(formData).forEach((key) => {
        if (record.hasOwnProperty(key)) {
          tmpData[key] = record[key];
        }
      });
      //赋值
      Object.assign(formData, tmpData);

      // 保存初始数据，特别是状态字段
      initialFormData.value = { ...tmpData };
    });
  }

  /**
   * 提交数据
   */
  async function submitForm() {
    try {
      // 触发表单验证
      await validate();
    } catch ({ errorFields }) {
      if (errorFields) {
        const firstField = errorFields[0];
        if (firstField) {
          formRef.value.scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
        }
      }
      return Promise.reject(errorFields);
    }
    confirmLoading.value = true;
    const isUpdate = ref<boolean>(false);
    //时间格式化
    let model = formData;
    if (model.id) {
      isUpdate.value = true;
    }
    //循环数据
    for (let data in model) {
      //如果该数据是数组并且是字符串类型
      if (model[data] instanceof Array) {
        let valueType = getValueType(formRef.value.getProps, data);
        //如果是字符串类型的需要变成以逗号分割的字符串
        if (valueType === 'string') {
          model[data] = model[data].join(',');
        }
      }
    }
    await updateRegGroupsStatus(model)
      .then((res) => {
        if (res.success) {
          createMessage.success(res.message);
          emit('ok');
        } else {
          createMessage.warning(res.message);
        }
      })
      .finally(() => {
        confirmLoading.value = false;
      });
  }
  watch(
    () => props.formData,
    (newVal) => {
      // 只在第一次设置时保存初始数据，避免后续修改影响初始值
      if (Object.keys(initialFormData.value).length === 0 && newVal && Object.keys(newVal).length > 0) {
        initialFormData.value = { ...newVal };
      }
    },
    { immediate: true, deep: true }
  );

  defineExpose({
    add,
    edit,
    submitForm,
  });
</script>

<style lang="less" scoped>
  .antd-modal-form {
    padding: 14px;
  }
</style>
