<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    @ok="handleOk"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <div class="check-report-form">
      <a-form ref="formRef" :model="formData" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol" layout="horizontal">
        <!-- 基本信息 -->
        <a-card title="基本信息" class="form-card" size="small">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="报告单号" name="checkBillNo">
                <a-input v-model:value="formData.checkBillNo" placeholder="请输入报告单号" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="异常标志" name="abnormalFlag">
                <a-select
                  size="small"
                  v-model:value="formData.abnormalFlag"
                  placeholder="请输入异常标志"
                  allow-clear
                  :options="[
                    { label: '正常', value: '0' },
                    { label: '异常', value: '1' },
                  ]"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="检查医生" name="doctorName">
                <a-input v-model:value="formData.doctorName" placeholder="请输入检查医生" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="仪器" name="instrument">
                <a-input v-model:value="formData.instrument" placeholder="请输入仪器名称" allow-clear />
              </a-form-item>
            </a-col>
          </a-row>
        </a-card>

        <!-- 检查信息 -->
        <a-card title="检查信息" class="form-card" size="small">
          <a-row :gutter="16">
            <a-col :span="24">
              <a-form-item label="检查目的" name="checkPurpose">
                <a-textarea v-model:value="formData.checkPurpose" placeholder="请输入检查目的" :rows="3" allow-clear />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="24">
              <a-form-item label="检查所见" name="checkObservations">
                <a-textarea v-model:value="formData.checkObservations" placeholder="请输入检查所见" :rows="4" allow-clear />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="24">
              <a-form-item label="检查结论" name="value">
                <a-textarea v-model:value="formData.value" placeholder="请输入检查结论" :rows="3" allow-clear />
              </a-form-item>
            </a-col>
          </a-row>
        </a-card>

        <!-- 医生信息 -->
        <a-card title="医生信息" class="form-card" size="small">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="报告医生" name="reportDoctorName">
                <a-input v-model:value="formData.reportDoctorName" placeholder="请输入报告医生" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="审核医生" name="auditDoctorName">
                <a-input v-model:value="formData.auditDoctorName" placeholder="请输入审核医生" allow-clear />
              </a-form-item>
            </a-col>
          </a-row>
        </a-card>

        <!-- 时间信息 -->
        <a-card title="时间信息" class="form-card" size="small">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item label="检查时间" name="checkTime">
                <a-date-picker
                  v-model:value="formData.checkTime"
                  placeholder="请选择检查时间"
                  show-time
                  format="YYYY-MM-DD HH:mm:ss"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="审核时间" name="auditTime">
                <a-date-picker
                  v-model:value="formData.auditTime"
                  placeholder="请选择审核时间"
                  show-time
                  format="YYYY-MM-DD HH:mm:ss"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="报告时间" name="reportTime">
                <a-date-picker
                  v-model:value="formData.reportTime"
                  placeholder="请选择报告时间"
                  show-time
                  format="YYYY-MM-DD HH:mm:ss"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-card>

        <!-- 报告路径 -->
        <a-card title="报告文件" class="form-card" size="small">
          <a-row :gutter="16">
            <a-col :span="24">
              <a-form-item label="输入方式" name="inputMode">
                <a-radio-group v-model:value="inputMode" @change="handleInputModeChange">
                  <a-radio value="manual">手动输入</a-radio>
                  <a-radio value="upload">文件上传</a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="24">
              <a-form-item label="报告路径" name="reportPdfInterface">
                <!-- 手动输入模式 -->
                <a-input
                  v-if="inputMode === 'manual'"
                  v-model:value="formData.reportPdfInterface"
                  placeholder="请输入报告PDF路径（仅支持PDF格式）"
                  allow-clear
                >
                  <template #suffix>
                    <a-button type="link" size="small" @click="previewFile" :disabled="!formData.reportPdfInterface">
                      <FileOutlined />
                      预览
                    </a-button>
                  </template>
                </a-input>

                <!-- 文件上传模式 -->
                <div v-else>
                  <!--                  <a-tabs v-model:activeKey="uploadFileType" @change="handleFileTypeChange">
                    <a-tab-pane key="pdf" tab="PDF文件">
                      <a-upload
                        :maxCount="1"
                        :fileList="pdfFileList"
                        accept=".pdf"
                        :beforeUpload="beforeUploadPdf"
                        @change="handlePdfUpload"
                        :action="uploadUrl"
                        :headers="uploadHeaders"
                        :data="{ biz: 'temp' }"
                      >
                        <a-button>
                          <UploadOutlined />
                          上传PDF文件
                        </a-button>
                      </a-upload>
                      <div v-if="formData.reportPdfInterface" class="upload-result">
                        <a-typography-text type="success">
                          <FileOutlined /> PDF文件路径：{{ formData.reportPdfInterface }}
                        </a-typography-text>
                      </div>
                    </a-tab-pane>-->
                  <!--                    <a-tab-pane key="image" tab="图片文件">-->
                  <a-upload
                    :maxCount="5"
                    :fileList="imageFileList"
                    accept=".jpg,.jpeg,.png"
                    :beforeUpload="beforeUploadImage"
                    @change="handleImageUpload"
                    :action="uploadUrl"
                    :headers="uploadHeaders"
                    :data="{ biz: 'temp' }"
                    listType="picture-card"
                    :multiple="true"
                  >
                    <div v-if="imageFileList.length < 5">
                      <UploadOutlined />
                      <div style="margin-top: 8px">上传图片</div>
                    </div>
                  </a-upload>
                  <div v-if="formData.pic && formData.pic.length > 0" class="upload-result">
                    <a-typography-text type="success"> <FileOutlined /> 已上传图片：{{ formData.pic.length }} 个文件 </a-typography-text>
                    <div class="pic-list">
                      <a-tag v-for="(picPath, index) in formData.pic" :key="index" closable @close="removePicItem(index)">
                        {{ picPath.split('/').pop() }}
                      </a-tag>
                    </div>
                  </div>
                  <!--                    </a-tab-pane>
                  </a-tabs>-->
                </div>
              </a-form-item>
            </a-col>
          </a-row>
        </a-card>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <a-space>
            <a-button type="primary" @click="handleSubmit" :loading="loading">
              <save-outlined />
              保存
            </a-button>
            <a-button @click="handleReset">
              <reload-outlined />
              重置
            </a-button>
            <a-button @click="handleCancel"> 取消 </a-button>
          </a-space>
        </div>
      </a-form>
    </div>
  </j-modal>
</template>

<script lang="ts" setup>
  import { ref, nextTick, defineExpose, reactive } from 'vue';
  import JModal from '/@/components/Modal/src/JModal/JModal.vue';
  import { FileOutlined, ReloadOutlined, SaveOutlined, UploadOutlined } from '@ant-design/icons-vue';
  import dayjs from 'dayjs';
  import type { Dayjs } from 'dayjs';
  import { message } from 'ant-design-vue';
  import { addResult, getResultByCustomerRegGroupId } from '@/views/datamanage/RegDataManage.api';
  import { uploadUrl } from '/@/api/common/api';
  import { getHeaders } from '/@/utils/common/compUtils';

  const title = ref<string>('');
  const width = ref<number>(800);
  const visible = ref<boolean>(false);
  const disableSubmit = ref<boolean>(false);
  const registerForm = ref();
  const emit = defineEmits(['register', 'success']);

  const customerRegItemGroup = ref();

  /**
   * 新增
   */
  function add() {
    title.value = '新增';
    visible.value = true;
    nextTick(() => {
      registerForm.value.add();
    });
  }

  /**
   * 编辑
   * @param record
   */
  function edit(record) {
    title.value = disableSubmit.value ? '详情' : '编辑';
    visible.value = true;
    nextTick(() => {
      registerForm.value.edit(record);
    });
  }
  function open(group) {
    visible.value = true;
    customerRegItemGroup.value = group;
    getResultByCustomerRegGroupId({ customerRegGroupId: group.id }).then((res) => {
      if (res.code === 200) {
        initFormData(res.result || {});
      } else {
        handleReset();
      }
    });
    // // 清空之前的选择
    // selectedRowKeys.value = [];
    // selectedRows.value = [];
    // dataSource.value = checkedGroupList;
    // toGroupIdList = groupIdList;
  }
  /**
   * 确定按钮点击事件
   */
  function handleOk() {
    handleSubmit();
  }

  /**
   * form保存回调事件
   */
  function submitCallback() {
    handleCancel();
    emit('success');
  }

  /**
   * 取消按钮回调事件
   */
  function handleCancel() {
    handleReset();
    visible.value = false;
  }
  // Props
  interface Props {
    initialData?: any;
    loading?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    initialData: () => ({}),
    loading: false,
  });

  // Form ref
  const formRef = ref();

  // Form layout
  const labelCol = { span: 6 };
  const wrapperCol = { span: 18 };

  // Form data
  const formData = reactive({
    checkBillNo: '',
    value: '',
    doctorName: '',
    instrument: '',
    checkPurpose: '',
    checkObservations: '',
    abnormalFlag: '',
    abnormalFlagDesc: '',
    reportDoctorName: '',
    auditDoctorName: '',
    checkTime: null as Dayjs | null,
    auditTime: null as Dayjs | null,
    reportTime: null as Dayjs | null,
    reportPdfInterface: '',
    pic: [], // 改为数组格式
  });

  // 输入模式：manual(手动输入) 或 upload(文件上传)
  const inputMode = ref('manual');

  // 上传文件类型：pdf 或 image
  const uploadFileType = ref('pdf');

  // 上传配置
  const uploadHeaders = getHeaders();

  // 文件列表
  const pdfFileList = ref([]);
  const imageFileList = ref([]);

  // Validation rules
  const rules = {
    checkBillNo: [{ required: true, message: '请输入报告单号', trigger: 'blur' }],
    value: [{ required: true, message: '请输入检查结论', trigger: 'blur' }],
    doctorName: [{ required: true, message: '请输入检查医生', trigger: 'blur' }],
    /* checkPurpose: [
      { required: true, message: '请输入检查目的', trigger: 'blur' }
    ],*/
    checkObservations: [{ required: true, message: '请输入检查所见', trigger: 'blur' }],
    abnormalFlag: [{ required: true, message: '请选择异常标志', trigger: 'change' }],
    reportDoctorName: [{ required: true, message: '请输入报告医生', trigger: 'blur' }],
    auditDoctorName: [{ required: true, message: '请输入审核医生', trigger: 'blur' }],
    checkTime: [{ required: true, message: '请选择检查时间', trigger: 'change' }],
    auditTime: [{ required: true, message: '请选择审核时间', trigger: 'change' }],
    reportTime: [{ required: true, message: '请选择报告时间', trigger: 'change' }],
    reportPdfInterface: [
      {
        validator: (rule, value) => {
          // 只有在手工输入模式下才验证PDF格式
          if (inputMode.value === 'manual' && value) {
            if (!value.toLowerCase().endsWith('.pdf')) {
              return Promise.reject('手工输入只支持PDF格式文件，请输入以.pdf结尾的文件路径');
            }
          }
          // 文件上传模式下不进行格式验证（由上传组件处理）
          return Promise.resolve();
        },
        trigger: ['blur', 'change'],
      },
    ],
  };

  // Methods
  const handleSubmit = async () => {
    try {
      // 验证至少有一个文件路径
      /*
      if (!formData.reportPdfInterface && (!formData.pic || formData.pic.length === 0)) {
        message.warning('请输入文件路径或上传文件');
        return;
      }
*/

      // 表单验证（包含动态的PDF格式验证）
      await formRef.value?.validate();
      // 转换时间格式和处理pic字段
      const submitData = {
        ...formData,
        checkTime: formData.checkTime ? formData.checkTime.format('YYYY-MM-DD HH:mm:ss') : null,
        auditTime: formData.auditTime ? formData.auditTime.format('YYYY-MM-DD HH:mm:ss') : null,
        reportTime: formData.reportTime ? formData.reportTime.format('YYYY-MM-DD HH:mm:ss') : null,
        checkConclusion: formData.value,
        customerRegItemGroupId: customerRegItemGroup.value.id,
        archivesNum: customerRegItemGroup.value.archivesNum,
        customerRegId: customerRegItemGroup.value.customerRegId,
        idCard: customerRegItemGroup.value.idCard,
        // 将pic数组转换为JSON字符串
        pic: formData.pic.length > 0 ? formData.pic : [],
      };
      addResult(submitData).then((res) => {
        if (res.code === 200) {
          message.success('新增成功');
          handleCancel();
          emit('success');
        } else {
          message.error(res.msg);
          handleCancel();
        }
      });
    } catch (error) {
      message.error('请检查表单填写是否正确');
    }
  };

  const handleReset = () => {
    formRef.value?.resetFields();
    // 清空所有验证错误信息
    formRef.value?.clearValidate();
    inputMode.value = 'manual'; // 重置为手动输入模式
    uploadFileType.value = 'pdf'; // 重置为PDF文件类型
    pdfFileList.value = []; // 清空PDF文件列表
    imageFileList.value = []; // 清空图片文件列表
    formData.pic = []; // 重置pic数组
  };

  const selectFile = () => {
    // 这里可以实现文件选择逻辑
    message.info('文件选择功能待实现');
  };

  // 处理输入模式切换
  const handleInputModeChange = (e) => {
    const mode = e.target.value;
    if (mode !== inputMode.value) {
      // 切换模式时清空当前路径
      formData.reportPdfInterface = '';
      formData.pic = [];
      pdfFileList.value = [];
      imageFileList.value = [];

      // 清空表单验证错误信息
      formRef.value?.clearValidate(['reportPdfInterface']);

      message.info(`已切换到${mode === 'manual' ? '手动输入' : '文件上传'}模式`);
    }
  };

  // 处理文件类型切换
  const handleFileTypeChange = (activeKey) => {
    // 切换文件类型时清空另一个字段
    if (activeKey === 'pdf') {
      formData.pic = [];
      imageFileList.value = [];
    } else {
      formData.reportPdfInterface = '';
      pdfFileList.value = [];
    }

    // 清空表单验证错误信息
    formRef.value?.clearValidate(['reportPdfInterface']);
  };

  // 手动输入验证
  const validateManualInput = () => {
    // 只在手工输入模式下进行验证
    if (inputMode.value === 'manual' && formData.reportPdfInterface) {
      if (!formData.reportPdfInterface.toLowerCase().endsWith('.pdf')) {
        message.warning('手动输入只支持PDF格式文件，请输入以.pdf结尾的文件路径');
        formData.reportPdfInterface = '';
        // 触发表单验证
        formRef.value?.validateFields(['reportPdfInterface']);
      }
    }
  };

  // PDF文件上传前验证
  const beforeUploadPdf = (file) => {
    const isPdf = file.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf');
    if (!isPdf) {
      message.error('只能上传PDF格式的文件！');
      return false;
    }
    return true;
  };

  // 图片文件上传前验证
  const beforeUploadImage = (file) => {
    const isImage = file.type.startsWith('image/') && (file.type === 'image/jpeg' || file.type === 'image/jpg' || file.type === 'image/png');
    if (!isImage) {
      message.error('只能上传JPG或PNG格式的图片文件！');
      return false;
    }
    return true;
  };

  // 处理PDF文件上传
  const handlePdfUpload = (info) => {
    const { file, fileList } = info;
    pdfFileList.value = fileList;

    if (file.status === 'done') {
      if (file.response && file.response.success) {
        formData.reportPdfInterface = file.response.message;
        formData.pic = []; // 清空图片字段数组
        imageFileList.value = []; // 清空图片文件列表
        message.success('PDF文件上传成功');
      } else {
        message.error('PDF文件上传失败');
      }
    } else if (file.status === 'error') {
      message.error('PDF文件上传失败');
    }
  };

  // 处理图片文件上传
  const handleImageUpload = (info) => {
    const { file, fileList } = info;
    imageFileList.value = fileList;

    if (file.status === 'done') {
      if (file.response && file.response.success) {
        // 将图片路径添加到数组中
        if (!formData.pic.includes(file.response.message)) {
          formData.pic.push(file.response.message);
        }
        formData.reportPdfInterface = ''; // 清空PDF字段
        pdfFileList.value = []; // 清空PDF文件列表
        message.success('图片文件上传成功');
      } else {
        message.error('图片文件上传失败');
      }
    } else if (file.status === 'error') {
      message.error('图片文件上传失败');
    } else if (file.status === 'removed') {
      // 处理文件删除
      if (file.response && file.response.success) {
        const index = formData.pic.indexOf(file.response.message);
        if (index > -1) {
          formData.pic.splice(index, 1);
        }
      }
    }
  };

  // 删除图片项
  const removePicItem = (index) => {
    formData.pic.splice(index, 1);
    // 同时从文件列表中移除对应项
    if (imageFileList.value[index]) {
      imageFileList.value.splice(index, 1);
    }
  };

  // 预览文件
  const previewFile = () => {
    if (!formData.reportPdfInterface) {
      message.warning('请先输入文件路径');
      return;
    }

    // 检查是否是PDF格式
    if (!formData.reportPdfInterface.toLowerCase().endsWith('.pdf')) {
      message.warning('只能预览PDF格式的文件');
      return;
    }

    // 检查是否是有效的URL或路径
    try {
      if (formData.reportPdfInterface.startsWith('http') || formData.reportPdfInterface.startsWith('https')) {
        // 如果是网络路径，在新窗口打开
        window.open(formData.reportPdfInterface, '_blank');
      } else {
        // 如果是本地路径，提示用户
        message.info('本地文件路径：' + formData.reportPdfInterface);
      }
    } catch (error) {
      message.error('无效的文件路径');
    }
  };

  // 初始化数据
  const initFormData = (data: any) => {
    Object.keys(formData).forEach((key) => {
      if (data[key] !== undefined) {
        if (key.includes('Time') && data[key]) {
          formData[key] = dayjs(data[key]);
        } else if (key === 'pic') {
          // 处理pic字段，如果是字符串则解析为数组
          if (typeof data[key] === 'string' && data[key]) {
            try {
              formData[key] = JSON.parse(data[key]);
            } catch (e) {
              // 如果解析失败，当作单个路径处理
              formData[key] = [data[key]];
            }
          } else if (Array.isArray(data[key])) {
            formData[key] = data[key];
          } else {
            formData[key] = [];
          }
        } else {
          formData[key] = data[key];
        }
      }
    });
  };

  // 监听初始数据变化
  if (props.initialData && Object.keys(props.initialData).length > 0) {
    initFormData(props.initialData);
  }

  defineExpose({
    open,
    add,
    edit,
    disableSubmit,
  });
</script>

<style lang="less">
  .check-report-form {
    .form-card {
      margin-bottom: 16px;

      :deep(.ant-card-head) {
        background-color: #fafafa;
        border-bottom: 1px solid #e8e8e8;

        .ant-card-head-title {
          font-weight: 600;
          color: #1890ff;
        }
      }
    }

    .form-actions {
      text-align: center;
      padding: 24px 0;
      border-top: 1px solid #e8e8e8;
      margin-top: 16px;
    }

    :deep(.ant-form-item-label) {
      font-weight: 500;
    }

    :deep(.ant-input),
    :deep(.ant-input-number),
    :deep(.ant-select-selector),
    :deep(.ant-picker) {
      border-radius: 6px;
    }

    :deep(.ant-card-body) {
      padding: 16px;
    }
  }
  /**隐藏样式-modal确定按钮 */
  .jee-hidden {
    display: none !important;
  }

  /* 文件上传结果显示样式 */
  .upload-result {
    margin-top: 8px;
    padding: 8px;
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
    border-radius: 4px;

    .pic-list {
      margin-top: 8px;

      .ant-tag {
        margin-bottom: 4px;
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
</style>
<style lang="less" scoped></style>
