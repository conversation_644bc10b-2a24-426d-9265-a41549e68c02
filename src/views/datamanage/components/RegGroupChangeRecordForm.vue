<template>
  <a-spin :spinning="confirmLoading">
    <JFormContainer :disabled="disabled">
      <template #detail>
        <a-form ref="formRef" class="antd-modal-form" :labelCol="labelCol" :wrapperCol="wrapperCol" name="RegGroupChangeRecordForm">
          <a-row>
            <a-col :span="24">
              <a-form-item label="体检号" v-bind="validateInfos.examNo" id="RegGroupChangeRecordForm-examNo" name="examNo">
                <a-input v-model:value="formData.examNo" placeholder="请输入体检号" allow-clear></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="检客姓名" v-bind="validateInfos.customerName" id="RegGroupChangeRecordForm-customerName" name="customerName">
                <a-input v-model:value="formData.customerName" placeholder="请输入检客姓名" allow-clear></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="项目名称" v-bind="validateInfos.regGroupName" id="RegGroupChangeRecordForm-regGroupName" name="regGroupName">
                <a-input v-model:value="formData.regGroupName" placeholder="请输入项目名称" allow-clear></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item
                label="变更前检查状态"
                v-bind="validateInfos.oldCheckStatus"
                id="RegGroupChangeRecordForm-oldCheckStatus"
                name="oldCheckStatus"
              >
                <a-input v-model:value="formData.oldCheckStatus" placeholder="请输入变更前检查状态" allow-clear></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item
                label="变更后检查状态"
                v-bind="validateInfos.newCheckStatus"
                id="RegGroupChangeRecordForm-newCheckStatus"
                name="newCheckStatus"
              >
                <a-input v-model:value="formData.newCheckStatus" placeholder="请输入变更后检查状态" allow-clear></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item
                label="变更前接口状态"
                v-bind="validateInfos.oldInferfaceStatus"
                id="RegGroupChangeRecordForm-oldInferfaceStatus"
                name="oldInferfaceStatus"
              >
                <a-input v-model:value="formData.oldInferfaceStatus" placeholder="请输入变更前接口状态" allow-clear></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item
                label="变更后接口状态"
                v-bind="validateInfos.newInterfaceStatus"
                id="RegGroupChangeRecordForm-newInterfaceStatus"
                name="newInterfaceStatus"
              >
                <a-input v-model:value="formData.newInterfaceStatus" placeholder="请输入变更后接口状态" allow-clear></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="变更原因" v-bind="validateInfos.reason" id="RegGroupChangeRecordForm-reason" name="reason">
                <span>{{ formData.reason }}</span>
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="变更凭证" v-bind="validateInfos.changePic" id="CustomerRegItemGroupForm-changePic" name="changePic">
                <j-image-upload :fileMax="0" v-model:value="formData.changePic" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </template>
    </JFormContainer>
  </a-spin>
</template>

<script lang="ts" setup>
  import { ref, reactive, defineExpose, nextTick, defineProps, computed, onMounted } from 'vue';
  import { defHttp } from '/@/utils/http/axios';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getValueType } from '/@/utils';
  import { saveOrUpdate } from '../RegGroupChangeRecord.api';
  import { Form } from 'ant-design-vue';
  import JFormContainer from '/@/components/Form/src/container/JFormContainer.vue';
  import JImageUpload from '../../../components/Form/src/jeecg/components/JImageUpload.vue';
  const props = defineProps({
    formDisabled: { type: Boolean, default: false },
    formData: { type: Object, default: () => ({}) },
    formBpm: { type: Boolean, default: true },
  });

  const formRef = ref();
  const useForm = Form.useForm;
  const emit = defineEmits(['register', 'ok']);
  const formData = reactive<Record<string, any>>({
    id: '',
    examNo: '',
    customerName: '',
    regGroupName: '',
    reason: '',
    oldCheckStatus: '',
    newCheckStatus: '',
    oldInferfaceStatus: '',
    newInterfaceStatus: '',
    changePic: '',
  });

  const { createMessage } = useMessage();
  const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 5 } });
  const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 16 } });
  const confirmLoading = ref<boolean>(false);
  //表单验证
  const validatorRules = reactive({
    reason: [{ required: true, message: '请输入变更原因!' }],
  });
  const { resetFields, validate, validateInfos } = useForm(formData, validatorRules, { immediate: false });

  // 表单禁用
  const disabled = computed(() => {
    if (props.formBpm === true) {
      if (props.formData.disabled === false) {
        return false;
      } else {
        return true;
      }
    }
    return props.formDisabled;
  });

  /**
   * 新增
   */
  function add() {
    edit({});
  }

  /**
   * 编辑
   */
  function edit(record) {
    nextTick(() => {
      resetFields();
      const tmpData = {};
      Object.keys(formData).forEach((key) => {
        if (record.hasOwnProperty(key)) {
          tmpData[key] = record[key];
        }
      });
      //赋值
      Object.assign(formData, tmpData);
    });
  }

  /**
   * 提交数据
   */
  async function submitForm() {
    try {
      // 触发表单验证
      await validate();
    } catch ({ errorFields }) {
      if (errorFields) {
        const firstField = errorFields[0];
        if (firstField) {
          formRef.value.scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
        }
      }
      return Promise.reject(errorFields);
    }
    confirmLoading.value = true;
    const isUpdate = ref<boolean>(false);
    //时间格式化
    let model = formData;
    if (model.id) {
      isUpdate.value = true;
    }
    //循环数据
    for (let data in model) {
      //如果该数据是数组并且是字符串类型
      if (model[data] instanceof Array) {
        let valueType = getValueType(formRef.value.getProps, data);
        //如果是字符串类型的需要变成以逗号分割的字符串
        if (valueType === 'string') {
          model[data] = model[data].join(',');
        }
      }
    }
    await saveOrUpdate(model, isUpdate.value)
      .then((res) => {
        if (res.success) {
          createMessage.success(res.message);
          emit('ok');
        } else {
          createMessage.warning(res.message);
        }
      })
      .finally(() => {
        confirmLoading.value = false;
      });
  }

  defineExpose({
    add,
    edit,
    submitForm,
  });
</script>

<style lang="less" scoped>
  .antd-modal-form {
    padding: 14px;
  }
</style>
