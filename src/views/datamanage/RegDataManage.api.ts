import { defHttp } from '/src/utils/http/axios';

enum Api {
  listReg4DataMange = '/dataManage/statusManage/list',
  regGroupsGroupByDepart = '/dataManage/statusManage/regGroupsGroupByDepart',
  getGroupsByRegIdAndDepartId = '/dataManage/statusManage/getGroupsByRegIdAndDepartId',
  updateRegGroupsStatus = '/reg/customerReg/updateStatus',
  copyResult = '/dataManage/statusManage/copyResult',
  addResult = '/dataManage/statusManage/addResult',
  getResultByCustomerRegGroupId = '/dataManage/statusManage/getResultByCustomerRegGroupId',
}

/**
 * 列表接口
 * @param params
 */
export const listReg4Interface = (params) => defHttp.get({ url: Api.listReg4DataMange, params });

export const getGroupsByRegIdAndDepartId = (params) => {
  return defHttp.get({ url: Api.getGroupsByRegIdAndDepartId, params });
};
export const regGroupsGroupByDepart = (params) => {
  return defHttp.get({ url: Api.regGroupsGroupByDepart, params });
};
export const getResultByCustomerRegGroupId = (params) => {
  return defHttp.get({ url: Api.getResultByCustomerRegGroupId, params }, { isTransformResponse: false });
};



export const updateRegGroupsStatus = (params) => {
  return defHttp.post({ url: Api.updateRegGroupsStatus, params }, { isTransformResponse: false });
}

export const copyResult = (params) => {
  return defHttp.post({ url: Api.copyResult, params }, { isTransformResponse: false });
}
export const addResult = (params) => {
  return defHttp.post({ url: Api.addResult, params }, { isTransformResponse: false });
}

