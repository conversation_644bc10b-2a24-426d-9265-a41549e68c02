import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '可约天数',
    align:"center",
    dataIndex: 'preDays'
  },
  {
    title: '单次费用',
    align:"center",
    dataIndex: 'preFee'
  },
  {
    title: '每日开始时间',
    align:"center",
    dataIndex: 'dayStartTime'
  },
  {
    title: '每日结束时间',
    align:"center",
    dataIndex: 'dayEndTime'
  },
  {
    title: '间隔数值',
    align:"center",
    dataIndex: 'intervalVal'
  },
  {
    title: '间隔单位',
    align:"center",
    dataIndex: 'intervalUnit_dictText'
  },
  {
    title: '间隔限制人数',
    align:"center",
    dataIndex: 'intervalLimit'
  },
  {
    title: '是否开启',
    align:"center",
    dataIndex: 'enableFlag',
    customRender:({text}) => {
      return render.renderSwitch(text, [{text:'是',value:'1'},{text:'否',value:'0'}])
    }
    },
   {
    title: '是否定时任务使用',
    align:"center",
    dataIndex: 'autoJobFlag',
    customRender:({text}) => {
      return render.renderSwitch(text, [{text:'是',value:'1'},{text:'否',value:'0'}])

  }
  },
];

//子表列表数据
export const scheduleTplColumns: BasicColumn[] = [
  {
    title: '开始时间',
    align:"center",
    dataIndex: 'startTime'
  },
  {
    title: '结束时间',
    align:"center",
    dataIndex: 'endTime'
  },
  {
    title: '限制数量',
    align:"center",
    dataIndex: 'limitAmount'
  },
  {
    title: '启用',
    align:"center",
    dataIndex: 'enableFlag',
    customRender:({text}) => {
      return  render.renderSwitch(text, [{text:'是',value:'1'},{text:'否',value:'0'}])
    },
  },
];

// 高级查询数据
export const superQuerySchema = {
  preDays: {title: '可约天数',order: 0,view: 'number', type: 'number',},
  preFee: {title: '单次费用',order: 1,view: 'number', type: 'number',},
  dayStartTime: {title: '每日开始时间',order: 2,view: 'text', type: 'string',},
  dayEndTime: {title: '每日结束时间',order: 3,view: 'text', type: 'string',},
  intervalVal: {title: '间隔数值',order: 4,view: 'number', type: 'number',},
  intervalUnit: {title: '间隔单位',order: 5,view: 'radio', type: 'string',dictCode: 'time_interval_unit',},
  intervalLimit: {title: '间隔限制人数',order: 6,view: 'number', type: 'number',},
  enableFlag: {title: '是否开启',order: 7,view: 'switch', type: 'string',},
  autoJobFlag: {title: '是否定时任务使用',order: 7,view: 'switch', type: 'string',},
};
