import { defHttp } from '/@/utils/http/axios';
import { useMessage } from "/@/hooks/web/useMessage";

const { createConfirm } = useMessage();

enum Api {
  list = '/appointment/appointmentSchedule/list',
  save='/appointment/appointmentSchedule/add',
  edit='/appointment/appointmentSchedule/edit',
  generateSchedule='/appointment/appointmentSchedule/generate',
  batchUpdateEnableFlag='/appointment/appointmentSchedule/batchUpdateEnableFlag',
  deleteOne = '/appointment/appointmentSchedule/delete',
  deleteBatch = '/appointment/appointmentSchedule/deleteBatch',
  importExcel = '/appointment/appointmentSchedule/importExcel',
  exportXls = '/appointment/appointmentSchedule/exportXls',
  queryById = '/appointment/appointmentSchedule//queryById',
}

/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;

/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params,handleSuccess) => {
  return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}

/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({url: Api.deleteBatch, data: params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}

/**
 * 保存或者更新
 * @param params
 * @param isUpdate
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params }, { isTransformResponse: false });
}

/**
 * 生成排班
 * @param params
 */
export const generateSchedule = (params) => {
  return defHttp.get({ url: Api.generateSchedule, params }, { isTransformResponse: false });
}
/**
 * 批量更新状态
 * @param params
 */
export const batchUpdateEnableFlag = (params) => {
  return defHttp.post({ url: Api.batchUpdateEnableFlag, params }, { isTransformResponse: false });
}
/**
 * 根据id查询
 * @param params
 */
export const queryById = (params) => {
  return defHttp.get({ url: Api.queryById, params }, { isTransformResponse: false });
}
