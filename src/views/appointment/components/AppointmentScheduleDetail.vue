<template>
  <a-modal
    :title="title"
    :width="width"
    :open="visible"
    @ok="close"
    :okButtonProps="{ class: { 'jee-hidden': true } }"
    @cancel="close"
    cancelText="关闭"
  >
    <a-descriptions size="small" :column="1" bordered>
      <a-descriptions-item label="开始时间">{{ formData.startTime }}</a-descriptions-item>
      <a-descriptions-item label="结束时间">{{ formData.endTime }}</a-descriptions-item>
      <a-descriptions-item label="所属日期">{{ formData.ownDate }}</a-descriptions-item>
      <a-descriptions-item label="限制数量">{{ formData.limitAmount }}</a-descriptions-item>
      <a-descriptions-item label="启用状态">{{ formData.enableFlag == '1' ? '是' : '否' }}</a-descriptions-item>
    </a-descriptions>
  </a-modal>
</template>
<script lang="ts" setup>
  import { ref, reactive, nextTick } from 'vue';
  import { queryById } from '@/views/appointment/AppointmentSchedule.api';

  const title = ref<string>('');
  const width = ref<string>('50%');
  const visible = ref<boolean>(false);
  const tableLoading = ref<boolean>(false);
  const formData = reactive<Record<string, any>>({});
  const dataSource = ref<Record<string, any>[]>([]);

  function fetchTableData() {
    tableLoading.value = true;
    queryById({ id: formData.id })
      .then((res) => {
        dataSource.value = res;
      })
      .finally(() => {
        tableLoading.value = false;
      });
  }
  /**
   * 编辑
   */
  function showDetail(record) {
    title.value = '详情';
    visible.value = true;
    console.log(record);
    nextTick(() => {
      //赋值
      Object.assign(formData, record);
      fetchTableData();
    });
  }

  function close() {
    visible.value = false;
  }

  defineExpose({
    showDetail,
  });
</script>

<style lang="less" scoped>
  .antd-modal-form {
    height: 500px !important;
    overflow-y: auto;
    padding: 14px;
  }
</style>
