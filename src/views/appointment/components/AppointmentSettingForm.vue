<template>
  <a-spin :spinning="confirmLoading">
    <JFormContainer :disabled="disabled">
      <template #detail>
        <a-form ref="formRef" class="antd-modal-form" :labelCol="labelCol" :wrapperCol="wrapperCol" name="AppointmentSettingForm">
          <a-row>
            <a-col :span="24">
              <a-form-item label="可约天数" v-bind="validateInfos.preDays" id="AppointmentSettingForm-preDays" name="preDays">
                <a-input-number v-model:value="formData.preDays" placeholder="请输入可约天数" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="单次费用" v-bind="validateInfos.preFee" id="AppointmentSettingForm-preFee" name="preFee">
                <a-input-number v-model:value="formData.preFee" placeholder="请输入单次费用" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="每日开始时间" v-bind="validateInfos.dayStartTime" id="AppointmentSettingForm-dayStartTime" name="dayStartTime">
                <!--								<a-input v-model:value="formData.dayStartTime" placeholder="请输入每日开始时间"  allow-clear ></a-input>-->
                <time-picker
                  placeholder="请输入每日开始时间"
                  value-format="HH:mm:ss"
                  v-model:value="formData.dayStartTime"
                  style="width: 100%"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="每日结束时间" v-bind="validateInfos.dayEndTime" id="AppointmentSettingForm-dayEndTime" name="dayEndTime">
                <!--								<a-input v-model:value="formData.dayEndTime" placeholder="请输入每日结束时间"  allow-clear ></a-input>-->
                <time-picker
                  placeholder="请输入每日结束时间"
                  value-format="HH:mm:ss"
                  v-model:value="formData.dayEndTime"
                  style="width: 100%"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="间隔数值" v-bind="validateInfos.intervalVal" id="AppointmentSettingForm-intervalVal" name="intervalVal">
                <a-input-number v-model:value="formData.intervalVal" placeholder="请输入间隔数值" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="间隔单位" v-bind="validateInfos.intervalUnit" id="AppointmentSettingForm-intervalUnit" name="intervalUnit">
                <j-dict-select-tag
                  type="radio"
                  v-model:value="formData.intervalUnit"
                  dictCode="interval_unit"
                  placeholder="请选择间隔单位"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="间隔限制人数" v-bind="validateInfos.intervalLimit" id="AppointmentSettingForm-intervalLimit" name="intervalLimit">
                <a-input-number v-model:value="formData.intervalLimit" placeholder="请输入间隔限制人数" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="是否开启" v-bind="validateInfos.enableFlag" id="AppointmentSettingForm-enableFlag" name="enableFlag">
                <j-switch v-model:value="formData.enableFlag" :options="[1, 0]"></j-switch>
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="是否定时任务使用" v-bind="validateInfos.enableFlag" id="AppointmentSettingForm-autoJobFlag" name="autoJobFlag">
                <j-switch v-model:value="formData.autoJobFlag" :options="[1, 0]"></j-switch>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </template>
    </JFormContainer>
  </a-spin>
</template>

<script lang="ts" setup>
  import { ref, reactive, defineExpose, nextTick, defineProps, computed, onMounted } from 'vue';
  import { defHttp } from '/@/utils/http/axios';
  import { useMessage } from '/@/hooks/web/useMessage';
  import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import JSwitch from '/@/components/Form/src/jeecg/components/JSwitch.vue';
  import { getValueType } from '/@/utils';
  import { saveOrUpdate } from '../AppointmentSetting.api';
  import { Form, TimePicker } from 'ant-design-vue';
  import JFormContainer from '/@/components/Form/src/container/JFormContainer.vue';

  const props = defineProps({
    formDisabled: { type: Boolean, default: false },
    formData: { type: Object, default: () => ({}) },
    formBpm: { type: Boolean, default: true },
  });
  const useForm = Form.useForm;
  const emit = defineEmits(['register', 'ok']);
  const formData = reactive<Record<string, any>>({
    id: '',
    preDays: undefined,
    preFee: undefined,
    dayStartTime: '',
    dayEndTime: '',
    intervalVal: undefined,
    intervalUnit: '',
    intervalLimit: undefined,
    enableFlag: '',
    autoJobFlag: '',
  });
  const { createMessage } = useMessage();
  const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 5 } });
  const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 16 } });
  const confirmLoading = ref<boolean>(false);
  //表单验证
  const validatorRules = reactive({
    preDays: [{ required: true, message: '请输入可约天数!' }],
    dayStartTime: [{ required: true, message: '请输入每日开始时间!' }],
    dayEndTime: [{ required: true, message: '请输入每日结束时间!' }],
    intervalVal: [{ required: true, message: '请输入间隔数值!' }],
    intervalUnit: [{ required: true, message: '请输入间隔单位!' }],
    intervalLimit: [{ required: true, message: '请输入间隔限制人数!' }],
    enableFlag: [{ required: true, message: '请输入是否开启!' }],
  });
  const { resetFields, validate, validateInfos } = useForm(formData, validatorRules, { immediate: false });
  const formRef = ref();
  // 表单禁用
  const disabled = computed(() => {
    if (props.formBpm === true) {
      if (props.formData.disabled === false) {
        return false;
      } else {
        return true;
      }
    }
    return props.formDisabled;
  });

  /**
   * 新增
   */
  function add() {
    edit({});
  }

  /**
   * 编辑
   */
  function edit(record) {
    nextTick(() => {
      resetFields();
      const tmpData = {};
      Object.keys(formData).forEach((key) => {
        if (record.hasOwnProperty(key)) {
          tmpData[key] = record[key];
        }
      });
      //赋值
      Object.assign(formData, tmpData);
    });
  }

  /**
   * 提交数据
   */
  async function submitForm() {
    try {
      // 触发表单验证
      await validate();
    } catch ({ errorFields }) {
      if (errorFields) {
        const firstField = errorFields[0];
        if (firstField) {
          formRef.value.scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
        }
      }
      return Promise.reject(errorFields);
    }
    confirmLoading.value = true;
    const isUpdate = ref<boolean>(false);
    //时间格式化
    let model = formData;
    if (model.id) {
      isUpdate.value = true;
    }
    //循环数据
    for (let data in model) {
      //如果该数据是数组并且是字符串类型
      if (model[data] instanceof Array) {
        let valueType = getValueType(formRef.value.getProps, data);
        //如果是字符串类型的需要变成以逗号分割的字符串
        if (valueType === 'string') {
          model[data] = model[data].join(',');
        }
      }
    }

    await saveOrUpdate(model, isUpdate.value)
      .then((res) => {
        if (res.success) {
          createMessage.success(res.message);
          emit('ok');
        } else {
          createMessage.warning(res.message);
        }
      })
      .catch((err) => {
        // 处理错误
        console.error('保存或更新失败:', err);
        createMessage.error('保存或更新过程中发生错误');
      })
      .finally(() => {
        confirmLoading.value = false;
      });
  }

  defineExpose({
    add,
    edit,
    submitForm,
  });
</script>

<style lang="less" scoped>
  .antd-modal-form {
    padding: 14px;
  }
</style>
