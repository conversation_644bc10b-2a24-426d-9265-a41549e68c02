<template>
  <a-spin :spinning="confirmLoading">
    <JFormContainer :disabled="disabled">
      <template #detail>
        <a-form ref="formRef" class="antd-modal-form" :labelCol="labelCol" :wrapperCol="wrapperCol" name="AppointmentScheduleForm">
          <a-row>
            <a-col :span="24">
              <a-form-item label="开始时间" v-bind="validateInfos.startTime" id="AppointmentScheduleForm-startTime" name="startTime">
                <time-picker
                  placeholder="请选择开始时间"
                  value-format="HH:mm:ss"
                  v-model:value="formData.startTime"
                  style="width: 100%"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="结束时间" v-bind="validateInfos.endTime" id="AppointmentScheduleForm-endTime" name="endTime">
                <time-picker placeholder="请选择结束时间" value-format="HH:mm:ss" v-model:value="formData.endTime" style="width: 100%" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="所属日期" v-bind="validateInfos.ownDate" id="AppointmentScheduleForm-ownDate" name="ownDate">
                <a-date-picker
                  placeholder="请选择所属日期"
                  v-model:value="formData.ownDate"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="启用状态" v-bind="validateInfos.enableFlag" id="AppointmentScheduleForm-enableFlag" name="enableFlag">
                <j-switch v-model:value="formData.enableFlag" :options="[1, 0]"></j-switch>
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="限制数量" v-bind="validateInfos.limitAmount" id="AppointmentScheduleForm-limitAmount" name="limitAmount">
                <a-input-number v-model:value="formData.limitAmount" placeholder="请输入限制数量" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="排班模版" v-bind="validateInfos.scheduleTplId" id="AppointmentScheduleForm-scheduleTplId" name="scheduleTplId">
                <j-dict-select-tag v-model:value="formData.scheduleTplId" dictCode="" placeholder="请选择排班模版" allow-clear />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </template>
    </JFormContainer>
  </a-spin>
</template>

<script lang="ts" setup>
  import { ref, reactive, defineExpose, nextTick, defineProps, computed, onMounted } from 'vue';
  import { defHttp } from '/@/utils/http/axios';
  import { useMessage } from '/@/hooks/web/useMessage';
  import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import JSwitch from '/@/components/Form/src/jeecg/components/JSwitch.vue';
  import { TimePicker } from 'ant-design-vue';
  import { getValueType } from '/@/utils';
  import { saveOrUpdate } from '../AppointmentSchedule.api';
  import { Form } from 'ant-design-vue';
  import JFormContainer from '/@/components/Form/src/container/JFormContainer.vue';
  const props = defineProps({
    formDisabled: { type: Boolean, default: false },
    formData: { type: Object, default: () => ({}) },
    formBpm: { type: Boolean, default: true },
  });
  const formRef = ref();
  const useForm = Form.useForm;
  const emit = defineEmits(['register', 'ok']);
  const formData = reactive<Record<string, any>>({
    id: '',
    startTime: '',
    endTime: '',
    ownDate: '',
    enableFlag: '',
    limitAmount: undefined,
    scheduleTplId: '',
  });
  const { createMessage } = useMessage();
  const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 5 } });
  const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 16 } });
  const confirmLoading = ref<boolean>(false);
  //表单验证
  const validatorRules = reactive({
    startTime: [{ required: true, message: '请输入开始时间!' }],
    endTime: [{ required: true, message: '请输入结束时间!' }],
    ownDate: [{ required: true, message: '请输入所属日期!' }],
    enableFlag: [{ required: true, message: '请输入启用状态!' }],
  });
  const { resetFields, validate, validateInfos } = useForm(formData, validatorRules, { immediate: false });

  // 表单禁用
  const disabled = computed(() => {
    if (props.formBpm === true) {
      if (props.formData.disabled === false) {
        return false;
      } else {
        return true;
      }
    }
    return props.formDisabled;
  });

  /**
   * 新增
   */
  function add() {
    edit({});
  }

  /**
   * 编辑
   */
  function edit(record) {
    nextTick(() => {
      resetFields();
      const tmpData = {};
      Object.keys(formData).forEach((key) => {
        if (record.hasOwnProperty(key)) {
          tmpData[key] = record[key];
        }
      });
      //赋值
      Object.assign(formData, tmpData);
    });
  }

  /**
   * 提交数据
   */
  async function submitForm() {
    try {
      // 触发表单验证
      await validate();
    } catch ({ errorFields }) {
      if (errorFields) {
        const firstField = errorFields[0];
        if (firstField) {
          formRef.value.scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
        }
      }
      return Promise.reject(errorFields);
    }
    confirmLoading.value = true;
    const isUpdate = ref<boolean>(false);
    //时间格式化
    let model = formData;
    if (model.id) {
      isUpdate.value = true;
    }
    //循环数据
    for (let data in model) {
      //如果该数据是数组并且是字符串类型
      if (model[data] instanceof Array) {
        let valueType = getValueType(formRef.value.getProps, data);
        //如果是字符串类型的需要变成以逗号分割的字符串
        if (valueType === 'string') {
          model[data] = model[data].join(',');
        }
      }
    }
    await saveOrUpdate(model, isUpdate.value)
      .then((res) => {
        if (res.success) {
          createMessage.success(res.message);
          emit('ok');
        } else {
          createMessage.warning(res.message);
        }
      })
      .finally(() => {
        confirmLoading.value = false;
      });
  }

  defineExpose({
    add,
    edit,
    submitForm,
  });
</script>

<style lang="less" scoped>
  .antd-modal-form {
    padding: 14px;
  }
</style>
