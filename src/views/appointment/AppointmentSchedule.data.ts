import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '所属日期',
    align: "center",
    dataIndex: 'ownDate',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
  },
  {
    title: '开始时间',
    align: "center",
    dataIndex: 'startTime'
  },
  {
    title: '结束时间',
    align: "center",
    dataIndex: 'endTime'
  },
  {
    title: '限制数量',
    align: "center",
    dataIndex: 'limitAmount'
  },
  {
    title: '启用状态',
    align: "center",
    dataIndex: 'enableFlag',
    customRender:({text}) => {
       return  render.renderSwitch(text, [{text:'是',value:'1'},{text:'否',value:'0'}]);
     },
  }/*,
  {
    title: '排班模版',
    align: "center",
    dataIndex: 'scheduleTplId'
  },*/
];

// 高级查询数据
export const superQuerySchema = {
  startTime: {title: '开始时间',order: 0,view: 'time', type: 'string',},
  endTime: {title: '结束时间',order: 1,view: 'time', type: 'string',},
  ownDate: {title: '所属日期',order: 2,view: 'date', type: 'string',},
  status: {title: '状态',order: 3,view: 'switch', type: 'string',},
  enableFlag: {title: '启用状态',order: 4,view: 'switch', type: 'string',},
  limitAmount: {title: '限制数量',order: 5,view: 'number', type: 'number',},
  scheduleTplId: {title: '排班模版',order: 6,view: 'list', type: 'string',dictCode: '',},
};
