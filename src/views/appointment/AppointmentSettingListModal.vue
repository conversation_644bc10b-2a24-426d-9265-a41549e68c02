<template>
  <a-modal title="选择预约设置" :open="visible" width="80%" @ok="handleOk" :destroy-on-close="true" @cancel="handleCancel" cancelText="关闭">
    <div style="height: 70vh; overflow-y: auto">
      <AppointmentSettingMainList ref="appointmentSettingMainList" @getSelectedSettingId="setSelectedSettingId" />
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import { defineExpose, nextTick, ref } from 'vue';
  import { ICustomerReg } from '#/types';
  import AppointmentSettingMainList from '@/views/appointment/AppointmentSettingMainList.vue';
  import { useListPage } from '@/hooks/system/useListPage';
  import { list } from '@/views/basicinfo/ItemGroupInterface.api';
  import { columns } from '@/views/basicinfo/ItemGroupInterface.data';

  const visible = ref<boolean>(false);
  const appointmentSettingMainList = ref();
  const emit = defineEmits(['success']);
  const selectedSettingId = ref<any>();

  function open() {
    visible.value = true;
    /*nextTick(() => {
      console.log('customerReg.value', customerReg.value);
      barcodeListRef.value.load(customerReg.value);
    });*/
  }
  function setSelectedSettingId(data) {
    selectedSettingId.value = data;
  }

  function handleOk() {
    emit('success', selectedSettingId);
    visible.value = false;
  }

  function handleCancel() {
    visible.value = false;
  }

  defineExpose({
    open,
  });
</script>

<style lang="less">
  /**隐藏样式-modal确定按钮 */
  .jee-hidden {
    display: none !important;
  }
  .full-modal {
    .ant-modal {
      max-width: 100%;
      top: 0;
      padding-bottom: 0;
      margin: 0;
    }
    .ant-modal-content {
      display: flex;
      flex-direction: column;
      min-height: calc(100vh);
    }
    .ant-modal-body {
      flex: 1;
    }
  }
</style>
