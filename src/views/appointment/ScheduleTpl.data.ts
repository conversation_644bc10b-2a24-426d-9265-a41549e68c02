import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  /*{
    title: '所属设置',
    align: "center",
    dataIndex: 'settingId_dictText'
  },*/
  {
    title: '开始时间',
    align: "center",
    dataIndex: 'startTime'
  },
  {
    title: '结束时间',
    align: "center",
    dataIndex: 'endTime'
  },
 /* {
    title: '状态',
    align: "center",
    dataIndex: 'status',
    customRender: ({text}) => {
      return render.renderSwitch(text, [{text: '是', value: '1'}, {text: '否', value: '0'}]);
    },
  },*/
  {
    title: '限制数量',
    align: "center",
    dataIndex: 'limitAmount'
  },
  {
    title: '启用',
    align: "center",
    dataIndex: 'enableFlag',
    customRender: ({text}) => {
      return render.renderSwitch(text, [{text: '是', value: '1'}, {text: '否', value: '0'}]);
    },
  },

];

// 高级查询数据
export const superQuerySchema = {
  settingId: {title: '所属设置',order: 0,view: 'list', type: 'string',dictCode: '',},
  startTime: {title: '开始时间',order: 1,view: 'time', type: 'string',},
  endTime: {title: '结束时间',order: 2,view: 'time', type: 'string',},
  status: {title: '状态',order: 3,view: 'switch', type: 'string',},
  enableFlag: {title: '启用',order: 4,view: 'switch', type: 'string',},
  limitAmount: {title: '限制数量',order: 5,view: 'number', type: 'number',},
};
