import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '不可约的日期',
    align: "center",
    dataIndex: 'unavailableDate',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
  },
  {
    title: '不可约开始时间',
    align: "center",
    dataIndex: 'startTime'
  },
  {
    title: '不可约结束时间',
    align: "center",
    dataIndex: 'endTime'
  },
  {
    title: '不可约原因',
    align: "center",
    dataIndex: 'reason'
  },
];

// 高级查询数据
export const superQuerySchema = {
  unavailableDate: {title: '不可约的日期',order: 0,view: 'date', type: 'string',},
  startTime: {title: '不可约开始时间',order: 1,view: 'time', type: 'string',},
  endTime: {title: '不可约结束时间',order: 2,view: 'time', type: 'string',},
  reason: {title: '不可约原因',order: 3,view: 'text', type: 'string',},
};
