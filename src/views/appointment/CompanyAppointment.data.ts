import { BasicColumn } from '/@/components/Table';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '预约单位',
    align: 'center',
    dataIndex: 'companyName',
  },
  {
    title: '体检人数',
    align: 'center',
    dataIndex: 'examCount',
  },
  {
    title: '人均预算',
    align: 'center',
    dataIndex: 'pepoleBudget',
  },
  {
    title: '联系人',
    align: 'center',
    dataIndex: 'contactName',
  },
  {
    title: '联系电话',
    align: 'center',
    dataIndex: 'contactPhone',
  },
  {
    title: '提交时间',
    align: 'center',
    dataIndex: 'createTime',
  },
  {
    title: '处理状态',
    align: 'center',
    dataIndex: 'dealStatus',
  },
  {
    title: '处理时间',
    align: 'center',
    dataIndex: 'updateTime',
  },
  {
    title: '处理人',
    align: 'center',
    dataIndex: 'updateBy',
  },
];

// 高级查询数据
export const superQuerySchema = {
  openId: { title: 'openId', order: 0, view: 'text', type: 'string' },
  customerId: { title: 'customerId', order: 1, view: 'text', type: 'string' },
  companyName: { title: '预约单位', order: 2, view: 'text', type: 'string' },
  examCount: { title: '体检人数', order: 3, view: 'number', type: 'number' },
  pepoleBudget: { title: '人均预算', order: 4, view: 'text', type: 'string' },
  contactName: { title: '联系人', order: 5, view: 'text', type: 'string' },
  contactPhone: { title: '联系电话', order: 6, view: 'text', type: 'string' },
};
