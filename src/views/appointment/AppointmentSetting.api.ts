import {defHttp} from '/@/utils/http/axios';
import { useMessage } from "/@/hooks/web/useMessage";

const { createConfirm } = useMessage();

enum Api {
  list = '/appointment/appointmentSetting/list',
  save= '/appointment/appointmentSetting/add',
  edit= '/appointment/appointmentSetting/edit',
  deleteOne = '/appointment/appointmentSetting/delete',
  deleteBatch = '/appointment/appointmentSetting/deleteBatch',
  importExcel = '/appointment/appointmentSetting/importExcel',
  exportXls = '/appointment/appointmentSetting/exportXls',
  scheduleTplList = '/appointment/appointmentSetting/listScheduleTplByMainId',
  scheduleTplSave= '/appointment/appointmentSetting/addScheduleTpl',
  scheduleTplEdit= '/appointment/appointmentSetting/editScheduleTpl',
  scheduleTplDelete = '/appointment/appointmentSetting/deleteScheduleTpl',
  scheduleTplDeleteBatch = '/appointment/appointmentSetting/deleteBatchScheduleTpl',
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;

/**
 * 列表接口
 * @param params
 */
export const list = (params) =>
  defHttp.get({ url: Api.list, params });

/**
 * 删除单个
 */
export const deleteOne = (params,handleSuccess) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
}

/**
 * 批量删除
 * @param params
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    }
  });
}

/**
 * 保存或者更新
 * @param params
 */
export const saveOrUpdate = (params, isUpdate) => {
  return new Promise((resolve, reject) => {
    let url = isUpdate ? Api.edit : Api.save;
    if (isUpdate) {
      createConfirm({
        iconType: 'warning',
        title: '确认发送',
        content: '是否更新已经生成的排班信息？',
        okText: '是',
        cancelText: '否',
        onOk: () => {
          params.updateSchedule = true;
          defHttp.post({ url: url, params }, { isTransformResponse: false })
            .then(res => resolve(res))
            .catch(err => reject(err));
        },
        onCancel: () => {
          params.updateSchedule = false;
          defHttp.post({ url: url, params }, { isTransformResponse: false })
            .then(res => resolve(res))
            .catch(err => reject(err));
        }
      });
    } else {
      defHttp.post({ url: url, params }, { isTransformResponse: false })
        .then(res => resolve(res))
        .catch(err => reject(err));
    }
  });
}
  
/**
 * 列表接口
 * @param params
 */
export const scheduleTplList = (params) => {
  if(params['settingId']){
    return defHttp.get({ url: Api.scheduleTplList, params });
  }
  return Promise.resolve({});
}

/**
 * 删除单个
 */
export const scheduleTplDelete = (params,handleSuccess) => {
  return defHttp.delete({ url: Api.scheduleTplDelete, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
}

/**
 * 批量删除
 * @param params
 */
export const scheduleTplDeleteBatch = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.scheduleTplDeleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    }
  });
}

/**
 * 保存或者更新
 * @param params
 */
export const  scheduleTplSaveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.scheduleTplEdit : Api.scheduleTplSave;
  return defHttp.post({ url: url, params },{ isTransformResponse: false });
}

/**
 * 导入
 */
export const scheduleTplImportUrl = '/org/jeecg/modules/appointment/appointmentSetting/importScheduleTpl'

/**
 * 导出
 */
export const scheduleTplExportXlsUrl = '/org/jeecg/modules/appointment/appointmentSetting/exportScheduleTpl'
