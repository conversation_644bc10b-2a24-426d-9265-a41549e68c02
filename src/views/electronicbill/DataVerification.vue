<template>
  <div class="data-verification">
    <a-card title="电子票据数据核对" style="margin-bottom: 16px">
      <a-form layout="inline" :model="queryForm" @submit.prevent="handleSearch">
        <a-form-item label="起始日期">
          <a-date-picker v-model:value="queryForm.startDate" format="YYYYMMDD" placeholder="请选择起始日期" />
        </a-form-item>
        <a-form-item label="截止日期">
          <a-date-picker v-model:value="queryForm.endDate" format="YYYYMMDD" placeholder="请选择截止日期" />
        </a-form-item>
        <a-form-item label="业务类型">
          <a-select v-model:value="queryForm.busType" placeholder="请选择业务类型" allow-clear style="width: 150px">
            <a-select-option value="01">住院</a-select-option>
            <a-select-option value="02">门诊</a-select-option>
            <a-select-option value="03">急诊</a-select-option>
            <a-select-option value="04">体检中心</a-select-option>
            <a-select-option value="05">门特</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="开票点编码">
          <a-input v-model:value="queryForm.placeCode" placeholder="请输入开票点编码" style="width: 200px" />
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" @click="checkTotalData">总笔数核对</a-button>
            <a-button @click="checkWriteOffData">冲红数据核对</a-button>
            <a-button type="dashed" @click="checkTotalDataByIvcDate">按开票日期总笔数核对</a-button>
            <a-button type="dashed" @click="checkWriteOffDataByIvcDate">按开票日期冲红数据核对</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 总笔数核对结果 -->
    <a-card v-if="totalDataResult" title="总笔数核对结果" style="margin-bottom: 16px">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-statistic title="正票笔数" :value="totalDataResult.normalBillCount" />
        </a-col>
        <a-col :span="6">
          <a-statistic title="正票金额" :value="totalDataResult.normalBillAmount" :precision="2" suffix="元" />
        </a-col>
        <a-col :span="6">
          <a-statistic title="红票笔数" :value="totalDataResult.redBillCount" />
        </a-col>
        <a-col :span="6">
          <a-statistic title="红票金额" :value="totalDataResult.redBillAmount" :precision="2" suffix="元" />
        </a-col>
      </a-row>
      <a-divider />
      <a-row :gutter="16">
        <a-col :span="12">
          <a-statistic title="合计笔数" :value="totalDataResult.totalBillCount" />
        </a-col>
        <a-col :span="12">
          <a-statistic title="合计金额" :value="totalDataResult.totalBillAmount" :precision="2" suffix="元" />
        </a-col>
      </a-row>
    </a-card>

    <!-- 冲红数据核对结果 -->
    <a-card v-if="writeOffDataResult" title="冲红数据核对结果" style="margin-bottom: 16px">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-statistic title="冲红笔数" :value="writeOffDataResult.writeOffBillCount" />
        </a-col>
        <a-col :span="12">
          <a-statistic title="冲红金额" :value="writeOffDataResult.writeOffBillAmount" :precision="2" suffix="元" />
        </a-col>
      </a-row>
    </a-card>

    <!-- 开票信息查询 -->
    <a-card title="开票信息查询">
      <a-form layout="inline" :model="billQueryForm" style="margin-bottom: 16px">
        <a-form-item label="日期类型">
          <a-select v-model:value="billQueryForm.dateType" style="width: 150px">
            <a-select-option value="ivcDate">开票日期</a-select-option>
            <a-select-option value="busDate">业务日期</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="数据类型">
          <a-select v-model:value="billQueryForm.dataType" placeholder="请选择数据类型" allow-clear style="width: 150px">
            <a-select-option value="1">正票</a-select-option>
            <a-select-option value="2">红票</a-select-option>
            <a-select-option value="3">正红票</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="queryBillList">查询开票信息</a-button>
        </a-form-item>
      </a-form>

      <!-- 开票信息列表 -->
      <a-table
        v-if="billList.length > 0"
        :columns="billColumns"
        :data-source="billList"
        :pagination="pagination"
        @change="handleTableChange"
        rowKey="billNo"
        size="small"
      >
        <template #action="{ record }">
          <a-space>
            <a @click="viewBillDetail(record)">查看详情</a>
          </a-space>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script>
  import { reactive, ref } from 'vue';
  import { message } from 'ant-design-vue';
  import {
    checkTotalData,
    checkWriteOffData,
    checkTotalDataByIvcDate,
    checkWriteOffDataByIvcDate,
    getBillsByDate,
    getBillsByBusDate,
  } from '@/api/electronicbill';
  import dayjs from 'dayjs';

  export default {
    name: 'DataVerification',
    setup() {
      // 查询表单
      const queryForm = reactive({
        startDate: null,
        endDate: null,
        busType: null,
        placeCode: null,
      });

      // 开票信息查询表单
      const billQueryForm = reactive({
        dateType: 'ivcDate',
        dataType: null,
      });

      // 核对结果
      const totalDataResult = ref(null);
      const writeOffDataResult = ref(null);

      // 开票信息列表
      const billList = ref([]);
      const pagination = reactive({
        current: 1,
        pageSize: 20,
        total: 0,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total) => `共 ${total} 条数据`,
      });

      // 表格列定义
      const billColumns = [
        {
          title: '业务流水号',
          dataIndex: 'busNo',
          width: 150,
        },
        {
          title: '票据代码',
          dataIndex: 'billBatchCode',
          width: 120,
        },
        {
          title: '票据号码',
          dataIndex: 'billNo',
          width: 120,
        },
        {
          title: '票据金额',
          dataIndex: 'totalAmt',
          width: 100,
          customRender: ({ text }) => `¥${text || 0}`,
        },
        {
          title: '开票日期',
          dataIndex: 'ivcDateTime',
          width: 150,
        },
        {
          title: '业务类型',
          dataIndex: 'busType',
          width: 100,
          customRender: ({ text }) => {
            const types = { '01': '住院', '02': '门诊', '03': '急诊', '04': '体检中心', '05': '门特' };
            return types[text] || text;
          },
        },
        {
          title: '票据状态',
          dataIndex: 'state',
          width: 100,
        },
        {
          title: '操作',
          key: 'action',
          width: 100,
          slots: { customRender: 'action' },
        },
      ];

      // 格式化日期
      const formatDate = (date) => {
        return date ? dayjs(date).format('YYYYMMDD') : '';
      };

      // 总笔数核对
      const handleCheckTotalData = async () => {
        if (!queryForm.startDate || !queryForm.endDate) {
          message.warning('请选择日期范围');
          return;
        }

        try {
          const params = {
            startDate: formatDate(queryForm.startDate),
            endDate: formatDate(queryForm.endDate),
            busType: queryForm.busType,
            placeCode: queryForm.placeCode,
          };

          const response = await checkTotalData(params);
          if (response.success) {
            totalDataResult.value = response.result;
            message.success('总笔数核对完成');
          } else {
            message.error('核对失败：' + response.message);
          }
        } catch (error) {
          message.error('核对异常：' + error.message);
        }
      };

      // 冲红数据核对
      const handleCheckWriteOffData = async () => {
        if (!queryForm.startDate || !queryForm.endDate) {
          message.warning('请选择日期范围');
          return;
        }

        try {
          const params = {
            startDate: formatDate(queryForm.startDate),
            endDate: formatDate(queryForm.endDate),
            busType: queryForm.busType,
            placeCode: queryForm.placeCode,
          };

          const response = await checkWriteOffData(params);
          if (response.success) {
            writeOffDataResult.value = response.result;
            message.success('冲红数据核对完成');
          } else {
            message.error('核对失败：' + response.message);
          }
        } catch (error) {
          message.error('核对异常：' + error.message);
        }
      };

      // 按开票日期总笔数核对
      const handleCheckTotalDataByIvcDate = async () => {
        if (!queryForm.startDate || !queryForm.endDate) {
          message.warning('请选择日期范围');
          return;
        }

        try {
          const params = {
            startDate: formatDate(queryForm.startDate),
            endDate: formatDate(queryForm.endDate),
            busType: queryForm.busType,
            placeCode: queryForm.placeCode,
          };

          const response = await checkTotalDataByIvcDate(params);
          if (response.success) {
            totalDataResult.value = response.result;
            message.success('按开票日期总笔数核对完成');
          } else {
            message.error('核对失败：' + response.message);
          }
        } catch (error) {
          message.error('核对异常：' + error.message);
        }
      };

      // 按开票日期冲红数据核对
      const handleCheckWriteOffDataByIvcDate = async () => {
        if (!queryForm.startDate || !queryForm.endDate) {
          message.warning('请选择日期范围');
          return;
        }

        try {
          const params = {
            startDate: formatDate(queryForm.startDate),
            endDate: formatDate(queryForm.endDate),
            busType: queryForm.busType,
            placeCode: queryForm.placeCode,
          };

          const response = await checkWriteOffDataByIvcDate(params);
          if (response.success) {
            writeOffDataResult.value = response.result;
            message.success('按开票日期冲红数据核对完成');
          } else {
            message.error('核对失败：' + response.message);
          }
        } catch (error) {
          message.error('核对异常：' + error.message);
        }
      };

      // 查询开票信息
      const handleQueryBillList = async () => {
        if (!queryForm.startDate || !queryForm.endDate) {
          message.warning('请选择日期范围');
          return;
        }

        try {
          const params = {
            startDate: billQueryForm.dateType === 'ivcDate' ? formatDate(queryForm.startDate) + '000000000' : formatDate(queryForm.startDate),
            endDate: billQueryForm.dateType === 'ivcDate' ? formatDate(queryForm.endDate) + '235959999' : formatDate(queryForm.endDate),
            placeCode: queryForm.placeCode,
            busType: queryForm.busType,
            dataType: billQueryForm.dataType,
            pageNo: pagination.current,
            pageSize: pagination.pageSize,
          };

          const apiCall = billQueryForm.dateType === 'ivcDate' ? getBillsByDate : getBillsByBusDate;
          const response = await apiCall(params);

          if (response.success) {
            billList.value = response.result.billList || [];
            pagination.total = response.result.total || 0;
            message.success('查询完成');
          } else {
            message.error('查询失败：' + response.message);
          }
        } catch (error) {
          message.error('查询异常：' + error.message);
        }
      };

      // 表格翻页
      const handleTableChange = (pag) => {
        pagination.current = pag.current;
        pagination.pageSize = pag.pageSize;
        handleQueryBillList();
      };

      // 查看票据详情
      const viewBillDetail = (record) => {
        // 这里可以打开详情弹窗或跳转详情页面
        message.info(`查看票据详情：${record.billNo}`);
      };

      return {
        queryForm,
        billQueryForm,
        totalDataResult,
        writeOffDataResult,
        billList,
        pagination,
        billColumns,
        checkTotalData: handleCheckTotalData,
        checkWriteOffData: handleCheckWriteOffData,
        checkTotalDataByIvcDate: handleCheckTotalDataByIvcDate,
        checkWriteOffDataByIvcDate: handleCheckWriteOffDataByIvcDate,
        queryBillList: handleQueryBillList,
        handleTableChange,
        viewBillDetail,
      };
    },
  };
</script>

<style scoped>
  .data-verification .ant-statistic {
    text-align: center;
  }
</style>
