<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="getTitle" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, computed, unref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { applyFormSchema } from '../electronicbill.data';
  import { applyElectronicBill } from '/@/api/electronicbill/electronicbill';
  import { useMessage } from '/@/hooks/web/useMessage';

  const emit = defineEmits(['success', 'register']);
  const { createMessage } = useMessage();

  const isUpdate = ref(true);
  const rowId = ref('');

  const [registerForm, { getFieldsValue, setFieldsValue, resetFields, validate }] = useForm({
    labelWidth: 100,
    schemas: applyFormSchema,
    showActionButtonGroup: false,
    baseColProps: { lg: 12, md: 24 },
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    resetFields();
    setModalProps({ confirmLoading: false });
    isUpdate.value = !!data?.isUpdate;

    if (unref(isUpdate)) {
      rowId.value = data.record.id;
      setFieldsValue({
        ...data.record,
      });
    }
  });

  const getTitle = computed(() => (!unref(isUpdate) ? '申请电子票据' : '编辑电子票据'));

  async function handleSubmit() {
    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });

      if (unref(isUpdate)) {
        // 编辑逻辑（如果需要）
        createMessage.success('编辑成功');
      } else {
        // 申请开票
        const result = await applyElectronicBill(values.billId);
        if (result.success) {
          createMessage.success('申请开票成功');
        } else {
          createMessage.error(result.message || '申请开票失败');
          setModalProps({ confirmLoading: false });
          return;
        }
      }

      closeModal();
      emit('success', { isUpdate: unref(isUpdate), values: { ...values, id: rowId.value } });
    } catch (error) {
      createMessage.error('操作失败');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
