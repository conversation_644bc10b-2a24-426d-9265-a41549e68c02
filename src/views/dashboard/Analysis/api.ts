import { defHttp } from '/@/utils/http/axios';

enum Api {
  loginfo = '/sys/loginfo',
  visitInfo = '/sys/visitInfo',
  sendQueueMessage = '/reg/customerReg/sendQueueMessage',
  testPay = '/fee/feePayRecord/testPay',
  fixData = '/station/customerRegItemResult/fixData',
}
/**
 * 日志统计信息
 * @param params
 */
export const getLoginfo = (params) => defHttp.get({ url: Api.loginfo, params }, { isTransformResponse: false });
/**
 * 访问量信息
 * @param params
 */
export const getVisitInfo = (params) => defHttp.get({ url: Api.visitInfo, params }, { isTransformResponse: false });

export const sendQueueMessage = (params) => defHttp.get({ url: Api.sendQueueMessage, params }, { isTransformResponse: false });

export const testPay = (params) => defHttp.get({ url: Api.testPay, params }, { isTransformResponse: false });

export const fixData = (params) => defHttp.get({ url: Api.fixData, params });
