<template>
  <div class="md:flex">
    <template v-for="(item, index) in growCardList" :key="item.title">
      <Card
        size="small"
        :loading="loading"
        :title="item.title"
        class="md:w-1/4 w-full !md:mt-0 !mt-4"
        :class="[index + 1 < 4 && '!md:mr-4']"
        :canExpan="false"
      >
        <template #extra>
          <Tag :color="item.color">{{ item.action }}</Tag>
        </template>

        <div class="py-4 px-4 flex justify-between">
          <a-typography-title :level="3">{{ item.value }}</a-typography-title>
          <Icon :icon="item.icon" :size="40" />
        </div>

        <div class="p-2 px-4 flex justify-between">
          <span>总{{ item.title }}</span>
          <a-typography-text>{{ item.total }}</a-typography-text>
        </div>
      </Card>
    </template>
  </div>
</template>
<script lang="ts" setup>
  import { Icon } from '/@/components/Icon';
  import { Card, Tag } from 'ant-design-vue';
  import { stat } from '@/views/reg/CustomerReg.api';
  import { onMounted, reactive, ref } from 'vue';

  const loading = ref(false);

  const growCardList = reactive([
    {
      title: '体检人数',
      icon: 'visit-count|svg',
      value: 0,
      total: 0,
      color: 'green',
      action: '本月',
      prefix: '',
    },
    {
      title: '体检费用',
      icon: 'total-sales|svg',
      value: 0,
      total: 0,
      color: 'blue',
      action: '本月',
      prefix: '￥',
    },
    {
      title: '危急值人数',
      icon: 'download-count|svg',
      value: 0,
      total: 0,
      color: 'orange',
      action: '日',
      prefix: '',
    },
    {
      title: '报告超时',
      icon: 'transaction|svg',
      value: 0,
      total: 0,
      color: 'purple',
      action: '日',
      prefix: '',
    },
  ]);
  const formater = new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    currencyDisplay: 'symbol',
    currencySign: 'standard',
    useGrouping: true,
  });

  onMounted(() => {
    loading.value = true;
    stat()
      .then((res) => {
        let data = res.result;
        growCardList[0].value = data.monthReg.value;
        growCardList[0].total = data.totalReg.value;
        growCardList[1].value = formater.format(data.monthRegAmount.value);
        growCardList[1].total = formater.format(data.totalRegAmount.value);
        growCardList[2].value = data.criticalToday.value;
        growCardList[2].total = data.criticalToday.value;
        growCardList[3].value = data.reportExpied.value;
        growCardList[3].total = data.reportExpied.value;
      })
      .finally(() => {
        loading.value = false;
      });
  });
</script>
