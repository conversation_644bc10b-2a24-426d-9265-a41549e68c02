<template>
  <!--  <a-button type="primary" @click="openReport">测试</a-button>
  打印报表-->
  <IndexDef v-if="indexStyle === 0" />
  <IndexChart v-if="indexStyle === 1" />
  <IndexBdc v-if="indexStyle == 2" />
  <IndexTask v-if="indexStyle == 3" />
  <!--  <div style="width: 100%; text-align: right; margin-top: 20px">
    请选择首页样式：
    <a-radio-group v-model:value="indexStyle">
      <a-radio :value="0">默认</a-radio>
      <a-radio :value="1">销量统计</a-radio>
      <a-radio :value="2">业务统计</a-radio>
      <a-radio :value="3">我的任务</a-radio>
    </a-radio-group>
  </div>-->
  <a-card title="知识库" style="margin-top: 20px">
    <MedKnowledgeListIndex />
  </a-card>
  <a-button @click="fixData" v-if="hasPermission('data:fix')">修复数据</a-button>
</template>
<script lang="ts" setup>
  import { onMounted, ref } from 'vue';
  import IndexDef from './homePage/IndexDef.vue';
  import IndexChart from './homePage/IndexChart.vue';
  import IndexBdc from './homePage/IndexBdc.vue';
  import IndexTask from './homePage/IndexTask.vue';
  import { fixData, sendQueueMessage } from '@/views/dashboard/Analysis/api';
  import { usePermission } from '@/hooks/web/usePermission';
  import MedKnowledgeListIndex from '@/views/basicinfo/MedKnowledgeListIndex.vue';

  const { hasPermission } = usePermission();

  const payTestBarCodeRef = ref();

  const indexStyle = ref(0);
  const reportRef = ref(null);

  function sendMsg() {
    sendQueueMessage({ message: '你好，mq' });
  }

  onMounted(() => {});
</script>
