import type { AppRouteModule } from '/@/router/types';

import { LAYOUT } from '/@/router/constant';
import { t } from '/@/hooks/web/useI18n';

const system: AppRouteModule = {
  path: '/system',
  name: 'System',
  component: LAYOUT,
  redirect: '/system/form-rule',
  meta: {
    orderNo: 2000,
    icon: 'ion:settings-outline',
    title: t('routes.system.moduleName'),
  },
  children: [
    {
      path: 'form-rule',
      name: 'FormRuleManagement',
      component: () => import('/@/views/system/FormRuleManagement.vue'),
      meta: {
        title: t('routes.system.formRule'),
        icon: 'ant-design:form-outlined',
      },
    },
  ],
};

export default system;
