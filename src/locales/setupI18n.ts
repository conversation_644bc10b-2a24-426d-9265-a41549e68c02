import type { App } from 'vue';
import type { I18n, I18nOptions } from 'vue-i18n';

import { createI18n } from 'vue-i18n';
import { setHtmlPageLang, setLoadLocalePool } from './helper';
// 移除首屏对 Pinia 的依赖，避免在 store 尚未完全就绪时访问导致报错

export let i18n: ReturnType<typeof createI18n>;

async function createI18nOptions(): Promise<I18nOptions> {
  // 首屏固定使用中文，避免在 Pinia 初始化前访问 store
  const locale = 'zh_CN';
  // 仅注册中文资源，强制加载 zh_CN
  const defaultLocal = await import(`./lang/zh_CN.ts`);
  const message = defaultLocal.default?.message ?? {};

  setHtmlPageLang(locale);
  setLoadLocalePool((loadLocalePool) => {
    loadLocalePool.push(locale);
  });

  return {
    legacy: false,
    locale: 'zh_C<PERSON>',
    fallbackLocale: 'zh_CN',
    messages: {
      zh_CN: message,
    },
    availableLocales: ['zh_CN'],
    sync: true, //If you don’t want to inherit locale from global scope, you need to set sync of i18n component option to false.
    silentTranslationWarn: true, // true - warning off
    missingWarn: false,
    silentFallbackWarn: true,
  };
}

// setup i18n instance with glob
export async function setupI18n(app: App) {
  const options = await createI18nOptions();
  i18n = createI18n(options) as I18n;
  app.use(i18n);
}
