<template>
  <a-card title="流程设计器" class="bpmn-modeler">
    <div ref="bpmnContainer" class="bpmn-container"></div>
    <div class="controls">
      <a-button type="primary" @click="saveBpmn">保存</a-button>
      <a-button type="default" @click="exportBpmn">导出</a-button>
      <a-upload accept=".bpmn" show-upload-list="false" @change="importBpmn">
        <a-button type="dashed">导入</a-button>
      </a-upload>
    </div>
  </a-card>
</template>

<script lang="ts" setup>
  import { onMounted, ref } from 'vue';
  import BpmnModeler from 'bpmn-js/lib/Modeler';
  import { UploadChangeParam } from 'ant-design-vue/es/upload/interface';

  // 定义 bpmn 容器和 Modeler 实例
  const bpmnContainer = ref<HTMLDivElement | null>(null);
  const bpmnModeler = ref<BpmnModeler | null>(null);

  // 初始化 BPMN Modeler 并导入默认的 BPMN 模型
  onMounted(() => {
    if (bpmnContainer.value) {
      bpmnModeler.value = new BpmnModeler({
        container: bpmnContainer.value,
      });
    }
  });

  // 保存 BPMN XML
  const saveBpmn = () => {
    if (bpmnModeler.value) {
      bpmnModeler.value.saveXML({ format: true }, (err, xml) => {
        if (err) {
          console.error('Error saving BPMN XML:', err);
        } else {
          console.log('BPMN XML saved:', xml);
        }
      });
    }
  };

  // 导出 BPMN XML 并下载
  const exportBpmn = () => {
    if (bpmnModeler.value) {
      bpmnModeler.value.saveXML({ format: true }, (err, xml) => {
        if (err) {
          console.error('Error exporting BPMN XML:', err);
        } else {
          const blob = new Blob([xml], { type: 'application/xml' });
          const link = document.createElement('a');
          link.href = URL.createObjectURL(blob);
          link.download = 'diagram.bpmn';
          link.click();
        }
      });
    }
  };

  // 导入 BPMN XML 文件
  const importBpmn = (info: UploadChangeParam) => {
    const file = info.fileList[0]?.originFileObj;
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const xml = e.target?.result as string;
        if (bpmnModeler.value) {
          bpmnModeler.value.importXML(xml, (err) => {
            if (err) {
              console.error('Error importing BPMN XML:', err);
            } else {
              console.log('BPMN XML imported successfully');
            }
          });
        }
      };
      reader.readAsText(file);
    }
  };
</script>

<style scoped>
  @import 'bpmn-js/dist/assets/diagram-js.css';
  @import 'bpmn-js/dist/assets/bpmn-font/css/bpmn-embedded.css';

  .bpmn-modeler {
    margin: 20px;
  }

  .bpmn-container {
    width: 100%;
    height: 500px;
    border: 1px solid #ccc;
    margin-bottom: 10px;
  }

  .controls {
    display: flex;
    gap: 10px;
  }
</style>
