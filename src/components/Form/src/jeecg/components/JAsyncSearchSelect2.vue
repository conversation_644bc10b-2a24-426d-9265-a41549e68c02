<template>
  <!--异步字典下拉搜素-->
  <a-select
    v-bind="attrs"
    v-model:value="selectedAsyncValue"
    showSearch
    labelInValue
    allowClear
    :mode="multiple ? 'multiple' : undefined"
    :getPopupContainer="getParentContainer"
    :placeholder="placeholder"
    :filterOption="isDictTable ? false : filterOption"
    :notFoundContent="loading ? undefined : null"
    @focus="handleAsyncFocus"
    @search="loadData"
    @change="handleAsyncChange"
    @popup-scroll="handlePopupScroll"
  >
    <template #notFoundContent>
      <a-spin size="small" />
    </template>
    <a-select-option v-for="d in options" :key="d?.value" :value="d?.value">{{ d?.text }}</a-select-option>
  </a-select>
</template>

<script lang="ts">
  import { useDebounceFn } from '@vueuse/core';
  import { computed, defineComponent, PropType, ref, unref, watch } from 'vue';
  import { propTypes } from '/@/utils/propTypes';
  import { useAttrs } from '/@/hooks/core/useAttrs';
  import { initDictOptions } from '/@/utils/dict/index';
  import { defHttp } from '/@/utils/http/axios';
  import { debounce } from 'lodash-es';
  import { setPopContainer } from '/@/utils';
  import { isObject } from '/@/utils/is';

  export default defineComponent({
    name: 'JAsyncSearchSelect2',
    inheritAttrs: false,
    props: {
      value: propTypes.string,
      dict: propTypes.string,
      dictOptions: {
        type: Array,
        default: () => [],
      },
      async: propTypes.bool.def(false),
      placeholder: propTypes.string,
      popContainer: propTypes.string,
      pageSize: propTypes.number.def(10),
      getPopupContainer: {
        type: Function,
        default: (node) => node?.parentNode,
      },
      //默认开启Y轴溢出位置调整，因此在可视空间不足时下拉框位置会自动上移，导致Select的输入框被遮挡。需要注意的是，默认情况是是可视空间，而不是所拥有的空间
      //update-begin-author:liusq date:2023-04-04 for:[issue/286]下拉搜索框遮挡问题
      adjustY: propTypes.bool.def(true),
      //update-end-author:liusq date:2023-04-04 for:[issue/286]下拉搜索框遮挡问题
      //是否在有值后立即触发change
      immediateChange: propTypes.bool.def(false),
      //update-begin-author:taoyan date:2022-8-15 for: VUEN-1971 【online 专项测试】关联记录和他表字段 1
      //支持传入查询参数，如排序信息
      params: {
        type: Object,
        default: () => {},
      },
      //update-end-author:taoyan date:2022-8-15 for: VUEN-1971 【online 专项测试】关联记录和他表字段 1
      // 新增API属性
      api: {
        type: Function as PropType<(params?: any) => Promise<any[]>>,
        default: null,
      },
      // 字段映射配置
      fieldMapping: {
        type: Object,
        default: () => ({
          value: 'value',
          text: 'text',
        }),
      },
      multiple: propTypes.bool.def(false),
    },
    emits: ['change', 'update:value', 'select'],
    setup(props, { emit, refs }) {
      const options = ref<any[]>([]);
      const loading = ref(false);
      // update-begin--author:liaozhiyang---date:20231205---for：【issues/897】JSearchSelect组件添加class/style样式不生效
      const attrs = useAttrs({ excludeDefaultKeys: false });
      const selectedAsyncValue = ref(null);
      const lastLoad = ref(0);
      // 是否根据value加载text
      const loadSelectText = ref(true);
      // 异步(字典表) - 滚动加载时会用到
      let isHasData = true;
      let scrollLoading = false;
      let pageNo = 1;
      let searchKeyword = '';

      // 新增计算属性：是否是自定义API模式
      const isCustomApi = computed(() => !!props.api);

      // 是否是字典表
      const isDictTable = computed(() => {
        return props.dict?.split(',').length >= 2 || isCustomApi.value;
      });

      /**
       * 监听字典code
       */
      watch(
        () => props.dict,
        () => {
          if (!props.dict) {
            return;
          }
          if (isDictTable.value) {
            initDictTableData();
          } else {
            initDictCodeData();
          }
        },
        { immediate: true }
      );

      /**
       * 监听api
       */
      watch(
        () => props.api,
        () => {
          if (!props.api) {
            return;
          }

          initDictTableData();
        },
        { immediate: true }
      );

      /**
       * 监听value
       */
      watch(
        () => props.value,
        (val) => {
          if (val) {
            initSelectValue();
          } else {
            selectedAsyncValue.value = null;
          }
        },
        { immediate: true }
      );
      /**
       * 监听dictOptions
       */
      watch(
        () => props.dictOptions,
        (val) => {
          if (val && val.length >= 0) {
            options.value = [...val];
          }
        },
        { immediate: true }
      );
      /**
       * 异步查询数据
       */
      // 修改后的loadData方法
      const loadData = debounce(async function loadData(value) {
        if (!isDictTable.value && !isCustomApi.value) return;

        pageNo = 1;
        isHasData = true;
        searchKeyword = value;

        lastLoad.value += 1;
        const currentLoad = unref(lastLoad);
        options.value = [];
        loading.value = true;

        try {
          const params = {
            keyword: value,
            pageSize: props.pageSize,
            pageNo,
            ...props.params, // 合并原有参数
          };

          let res;
          if (isCustomApi.value) {
            // 调用自定义API
            res = await props.api(params);
          } else {
            // 原有字典表逻辑
            res = await defHttp.get({
              url: `/sys/dict/loadDict/${props.dict}`,
              params,
            });
          }
          if (currentLoad !== unref(lastLoad)) return;
          if (res?.length) {
            // 转换字段结构
            options.value = res.map((item) => ({
              value: item[props.fieldMapping.value],
              text: item[props.fieldMapping.text],
            }));
            pageNo++;
          } else {
            isHasData = pageNo === 1 ? false : isHasData;
          }
          loading.value = false;
        } catch (e) {
          loading.value = false;
          console.error('数据加载失败:', e);
        }
      }, 300);
      /**
       * 初始化value
       */
      async function initSelectValue() {
        if (!props.value) {
          selectedAsyncValue.value = null;
          return;
        }

        // 阻止重复加载
        if (loadSelectText.value === false) {
          loadSelectText.value = true;
          return;
        }

        const { value, dict } = props;

        try {
          if (!selectedAsyncValue.value?.key || selectedAsyncValue.value.key !== value) {
            let res;
            let selectedValues = [];
            // 自定义API模式
            if (isCustomApi.value) {
              // 调用自定义API获取标签
              res = await props.api({
                [props.fieldMapping.value]: value,
                pageSize: 1,
                ...props.params,
              });
            } else if (isDictTable.value) {
              res = await defHttp.get({
                url: `/sys/dict/loadDictItemList/${dict}`,
                params: { key: value },
              });
            }
            //将res强制转换为数组
            res = Array.isArray(res) ? res : [res];
            if (res.length == 0) {
              selectedAsyncValue.value = null;
              return;
            }

            selectedValues = res.map((item) => ({
              key: item[props.fieldMapping.value],
              label: item[props.fieldMapping.text],
            }));

            selectedAsyncValue.value = props.multiple ? selectedValues : selectedValues[0];

            emit(
              'select',
              selectedValues.map((item) => ({
                value: item.key,
                text: item.label,
              }))
            );
            // 立即触发change（如果需要）
            if (props.immediateChange) {
              emit('change', value);
            }
          }
        } catch (e) {
          console.error('初始化选中值失败:', e);
        } finally {
          loadSelectText.value = true;
        }
      }

      /**
       * 初始化字典下拉数据
       */
      async function initDictTableData() {
        let { dict, pageSize, api } = props;

        if (!isDictTable.value && !isCustomApi.value) return;

        // Reset pagination and data flags
        pageNo = 1;
        isHasData = true;
        searchKeyword = '';

        // Load initial data
        loading.value = true;
        let keywordInfo = getKeywordParam('');
        try {
          let res;
          if (isCustomApi.value) {
            // Use custom API
            res = await api({ pageSize, keyword: keywordInfo, pageNo, ...props.params });
          } else {
            // Use default dictionary API
            res = await defHttp.get({
              url: `/sys/dict/loadDict/${dict}`,
              params: { pageSize, keyword: keywordInfo, pageNo },
            });
          }

          if (res && res.length > 0) {
            options.value = res.map((item) => ({
              value: item[props.fieldMapping.value],
              text: item[props.fieldMapping.text],
            }));
            pageNo++;
          } else {
            isHasData = false;
          }
        } catch (error) {
          console.error('Failed to load data:', error);
        } finally {
          loading.value = false;
        }
      }

      /**
       * 查询数据字典
       */
      async function initDictCodeData() {
        options.value = await initDictOptions(props.dict);
      }

      /**
       * 异步改变事件
       * */
      function handleAsyncChange(selectedObj) {
        if (selectedObj) {
          selectedAsyncValue.value = selectedObj;
        } else {
          selectedAsyncValue.value = null;
          options.value = [];
          loadData('');
        }
        callback();
        // update-begin--author:liaozhiyang---date:20240524---for：【TV360X-426】下拉搜索设置了默认值，把查询条件删掉，再点击重置，没附上值
        // 点x清空时需要把loadSelectText设置true
        selectedObj ?? (loadSelectText.value = true);
        // update-end--author:liaozhiyang---date:20240524---for：【TV360X-426】下拉搜索设置了默认值，把查询条件删掉，再点击重置，没附上值
      }

      function callback() {
        loadSelectText.value = false;
        let targetValue;
        let temp = [];

        // 异步模式
        if (Array.isArray(selectedAsyncValue.value)) {
          // 多选
          targetValue = selectedAsyncValue.value.map((item) => item.key).join(',');
          selectedAsyncValue.value.forEach((selectedItem) => {
            let item = options.value.find((opt) => opt.value == selectedItem.key);
            if (item) {
              temp.push(item);
            }
          });
        } else if (selectedAsyncValue.value) {
          // 单选
          targetValue = selectedAsyncValue.value.key;
          let item = options.value.find((opt) => opt.value == selectedAsyncValue.value.key);
          if (item) {
            temp.push(item);
          }
        }

        emit('change', targetValue);
        emit('update:value', targetValue);
        emit('select', temp);
      }

      /**
       * 过滤选中option
       */
      function filterOption(input, option) {
        //update-begin-author:taoyan date:2022-11-8 for: issues/218 所有功能表单的下拉搜索框搜索无效
        let value = '',
          label = '';
        try {
          value = option.value;
          label = option.children()[0].children;
        } catch (e) {
          console.log('获取下拉项失败', e);
        }
        let str = input.toLowerCase();
        return value.toLowerCase().indexOf(str) >= 0 || label.toLowerCase().indexOf(str) >= 0;
        //update-end-author:taoyan date:2022-11-8 for: issues/218 所有功能表单的下拉搜索框搜索无效
      }

      function getParentContainer(node) {
        // update-begin-author:taoyan date:20220407 for: getPopupContainer一直有值 导致popContainer的逻辑永远走不进去，把它挪到前面判断
        if (props.popContainer) {
          // update-begin--author:liaozhiyang---date:20240517---for：【QQYUN-9339】有多个modal弹窗内都有下拉字典多选和下拉搜索组件时，打开另一个modal时组件的options不展示
          return setPopContainer(node, props.popContainer);
          // update-end--author:liaozhiyang---date:20240517---for：【QQYUN-9339】有多个modal弹窗内都有下拉字典多选和下拉搜索组件时，打开另一个modal时组件的options不展示
        } else {
          if (typeof props.getPopupContainer === 'function') {
            return props.getPopupContainer(node);
          } else {
            return node?.parentNode;
          }
        }
        // update-end-author:taoyan date:20220407 for: getPopupContainer一直有值 导致popContainer的逻辑永远走不进去，把它挪到前面判断
      }

      //update-begin-author:taoyan date:2022-8-15 for: VUEN-1971 【online 专项测试】关联记录和他表字段 1
      //获取关键词参数 支持设置排序信息
      function getKeywordParam(text) {
        // 如果设定了排序信息，需要写入排序信息，在关键词后加 [orderby:create_time,desc]
        if (props.params && props.params.column && props.params.order) {
          let temp = text || '';

          //update-begin-author:taoyan date:2023-5-22 for: /issues/4905 表单生成器字段配置时，选择关联字段，在进行高级配置时，无法加载数据库列表，提示 Sgin签名校验错误！ #4905
          temp = temp + '[orderby:' + props.params.column + ',' + props.params.order + ']';
          return encodeURI(temp);
          //update-end-author:taoyan date:2023-5-22 for: /issues/4905 表单生成器字段配置时，选择关联字段，在进行高级配置时，无法加载数据库列表，提示 Sgin签名校验错误！ #4905
        } else {
          return text;
        }
      }
      //update-end-author:taoyan date:2022-8-15 for: VUEN-1971 【online 专项测试】关联记录和他表字段 1
      // update-begin--author:liaozhiyang---date:20240523---for：【TV360X-26】下拉搜索控件选中选项后再次点击下拉应该显示初始的下拉选项，而不是只展示选中结果
      const handleAsyncFocus = () => {
        // update-begin--author:liaozhiyang---date:20240709---for：【issues/6681】异步查询不生效
        if ((isObject(selectedAsyncValue.value) || selectedAsyncValue.value?.length) && (isDictTable.value || isCustomApi.value)) {
          // update-begin--author:liaozhiyang---date:20240809---for：【TV360X-2062】下拉搜索选择第二页数据后，第一次点击时(得到焦点)滚动条没复原到初始位置且数据会加载第二页数据(应该只加载第一页数据)
          options.value = [];
          // update-end--author:liaozhiyang---date:20240809---for：【TV360X-2062】下拉搜索选择第二页数据后，第一次点击时(得到焦点)滚动条没复原到初始位置且数据会加载第二页数据(应该只加载第一页数据)
          initDictTableData();
        }
        // update-end--author:liaozhiyang---date:20240709---for：【issues/6681】异步查询不生效
        attrs.onFocus?.();
      };
      // update-end--author:liaozhiyang---date:20240523---for：【TV360X-26】下拉搜索控件选中选项后再次点击下拉应该显示初始的下拉选项，而不是只展示选中结果

      /**
       * 2024-07-30
       * liaozhiyang
       * 【TV360X-1898】JsearchSelect组件传入字典表格式则支持滚动加载
       * */
      // 修改滚动加载处理
      const handlePopupScroll = async (e) => {
        if (!isHasData || scrollLoading || !(isDictTable.value || isCustomApi.value)) return;

        const { target } = e;
        const { scrollTop, scrollHeight, clientHeight } = target;
        if (scrollTop + clientHeight < scrollHeight - 10) return;

        scrollLoading = true;
        try {
          const params = {
            keyword: searchKeyword,
            pageSize: props.pageSize,
            pageNo,
            ...props.params,
          };

          let res;
          if (isCustomApi.value) {
            res = await props.api(params);
          } else {
            res = await defHttp.get({
              url: `/sys/dict/loadDict/${props.dict}`,
              params,
            });
          }

          if (res?.length) {
            const mapped = res.map((item) => ({
              value: item[props.fieldMapping.value],
              text: item[props.fieldMapping.text],
            }));
            options.value.push(...mapped);
            pageNo++;
          } else {
            isHasData = false;
          }
        } catch (e) {
          console.error('滚动加载失败:', e);
        } finally {
          scrollLoading = false;
        }
      };

      return {
        attrs,
        options,
        loading,
        isDictTable,
        selectedAsyncValue,
        loadData: useDebounceFn(loadData, 800),
        getParentContainer,
        filterOption,
        handleAsyncChange,
        handleAsyncFocus,
        handlePopupScroll,
      };
    },
  });
</script>

<style scoped></style>
