import type { App } from 'vue';

// 注册全局聊天表情包
//import { Picker } from 'emoji-mart-vue-fast/src';
// 注册全局dayjs
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import customParseFormat from 'dayjs/plugin/customParseFormat';
 // 解决日期时间国际化问题
 import 'dayjs/locale/zh-cn';

export async function registerThirdComp(app: App) {
  //---------------------------------------------------------------------
  // 不再在首屏/空闲时预注册 JVxe，改为首个使用点再注册
  //---------------------------------------------------------------------
  // 注册全局聊天表情包
  //app.component('Picker', Picker);
  
  //---------------------------------------------------------------------
  // 注册全局dayjs
  dayjs.locale('zh-cn');
  dayjs.extend(relativeTime);
  dayjs.extend(customParseFormat);
  app.config.globalProperties.$dayjs = dayjs
  app.provide('$dayjs', dayjs)
  //---------------------------------------------------------------------
}
