/*
// Remove borders from all Ant Design input components

// Regular inputs
.ant-input {
  border: none !important;
  box-shadow: none !important;
  
  &:focus, &:hover {
    border: none !important;
    box-shadow: none !important;
    outline: none !important;
  }
}

// Input with affixes
.ant-input-affix-wrapper {
  border: none !important;
  box-shadow: none !important;
  
  &:focus, &:hover, &-focused {
    border: none !important;
    box-shadow: none !important;
    outline: none !important;
  }
}

// Textareas
textarea.ant-input {
  border: none !important;
  box-shadow: none !important;
  
  &:focus, &:hover {
    border: none !important;
    box-shadow: none !important;
    outline: none !important;
  }
}

// Select components
.ant-select {
  .ant-select-selector {
    border: none !important;
    box-shadow: none !important;
    
    &:focus, &:hover {
      border: none !important;
      box-shadow: none !important;
      outline: none !important;
    }
  }
  
  &-focused .ant-select-selector {
    border: none !important;
    box-shadow: none !important;
    outline: none !important;
  }
}

// Auto-complete
.ant-auto-complete {
  .ant-select-selector {
    border: none !important;
    box-shadow: none !important;
    
    &:focus, &:hover {
      border: none !important;
      box-shadow: none !important;
      outline: none !important;
    }
  }
}

// Input number
.ant-input-number {
  border: none !important;
  box-shadow: none !important;
  
  &:focus, &:hover, &-focused {
    border: none !important;
    box-shadow: none !important;
    outline: none !important;
  }
  
  .ant-input-number-input {
    border: none !important;
    box-shadow: none !important;
    
    &:focus, &:hover {
      border: none !important;
      box-shadow: none !important;
      outline: none !important;
    }
  }
}

// Date picker
.ant-picker {
  border: none !important;
  box-shadow: none !important;
  
  &:focus, &:hover, &-focused {
    border: none !important;
    box-shadow: none !important;
    outline: none !important;
  }
}

// Time picker
.ant-time-picker-input {
  border: none !important;
  box-shadow: none !important;
  
  &:focus, &:hover {
    border: none !important;
    box-shadow: none !important;
    outline: none !important;
  }
}
*/
