package org.jeecg.modules.basicinfo.util;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @Description: 编码工具类测试
 * @Author: jeecg-boot
 * @Date: 2025-01-27
 * @Version: V1.0
 */
@Slf4j
@SpringBootTest
public class EncodingUtilTest {

    @Test
    public void testPinyinUtil() {
        log.info("=== 测试拼音工具类 ===");
        
        // 测试数据
        String[] testStrings = {
            "电焊工",
            "机械操作员",
            "危害因素",
            "噪声粉尘",
            "高温低温",
            "化学物质",
            "Test123",
            "测试ABC",
            ""
        };
        
        for (String testStr : testStrings) {
            String firstChars = PinyinUtil.getFirstChars(testStr);
            String fullPinyin = PinyinUtil.getFullPinyin(testStr);
            String smartHelpChar = PinyinUtil.generateSmartHelpChar(testStr);
            String smartPinyin = PinyinUtil.generateSmartPinyin(testStr);
            boolean containsChinese = PinyinUtil.containsChinese(testStr);
            
            log.info("原文: '{}' -> 首字母: '{}', 全拼: '{}', 智能助记码: '{}', 智能全拼: '{}', 包含中文: {}", 
                    testStr, firstChars, fullPinyin, smartHelpChar, smartPinyin, containsChinese);
        }
    }

    @Test
    public void testWubiUtil() {
        log.info("=== 测试五笔工具类 ===");
        
        // 测试数据
        String[] testStrings = {
            "电焊工",
            "机械操作员", 
            "危害因素",
            "噪声粉尘",
            "高温低温",
            "化学物质",
            "Test123",
            "测试ABC",
            "一地在要工",
            "上是中国同",
            ""
        };
        
        for (String testStr : testStrings) {
            String wubiCode = WubiUtil.getWubiCode(testStr);
            String fullWubiCode = WubiUtil.getFullWubiCode(testStr);
            String smartWubiCode = WubiUtil.generateSmartWubiCode(testStr);
            boolean containsChinese = WubiUtil.containsChinese(testStr);
            
            log.info("原文: '{}' -> 五笔简码: '{}', 完整五笔: '{}', 智能五笔: '{}', 包含中文: {}", 
                    testStr, wubiCode, fullWubiCode, smartWubiCode, containsChinese);
        }
    }

    @Test
    public void testCommonRiskFactors() {
        log.info("=== 测试常见危害因素编码 ===");
        
        // 常见危害因素
        String[] riskFactors = {
            "噪声",
            "粉尘", 
            "化学物质",
            "高温",
            "低温",
            "辐射",
            "振动",
            "紫外线",
            "电磁场",
            "缺氧",
            "密闭空间",
            "重复性劳动",
            "强迫体位",
            "生物因子",
            "心理社会因素"
        };
        
        log.info("危害因素编码对照表:");
        log.info("名称\t\t拼音首字母\t五笔简码\t完整拼音\t\t完整五笔");
        log.info("------------------------------------------------------------");
        
        for (String factor : riskFactors) {
            String pinyinFirst = PinyinUtil.getFirstChars(factor);
            String wubiCode = WubiUtil.getWubiCode(factor);
            String fullPinyin = PinyinUtil.getFullPinyin(factor);
            String fullWubi = WubiUtil.getFullWubiCode(factor);
            
            log.info("{}\t\t{}\t\t{}\t\t{}\t\t{}", 
                    factor, pinyinFirst, wubiCode, fullPinyin, fullWubi);
        }
    }

    @Test
    public void testSpecialCases() {
        log.info("=== 测试特殊情况 ===");
        
        // 测试特殊情况
        String[] specialCases = {
            null,
            "",
            "   ",
            "123",
            "ABC",
            "abc",
            "中英混合Test",
            "数字123中文",
            "特殊符号@#$%",
            "多音字：银行、行走",
            "生僻字：龘靐齉"
        };
        
        for (String testCase : specialCases) {
            try {
                String pinyinFirst = PinyinUtil.getFirstChars(testCase);
                String wubiCode = WubiUtil.getWubiCode(testCase);
                String smartHelpChar = PinyinUtil.generateSmartHelpChar(testCase);
                String smartWubiCode = WubiUtil.generateSmartWubiCode(testCase);
                
                log.info("特殊情况: '{}' -> 拼音首字母: '{}', 五笔简码: '{}', 智能助记码: '{}', 智能五笔: '{}'", 
                        testCase, pinyinFirst, wubiCode, smartHelpChar, smartWubiCode);
            } catch (Exception e) {
                log.error("处理特殊情况 '{}' 时出错: {}", testCase, e.getMessage());
            }
        }
    }

    @Test
    public void testPerformance() {
        log.info("=== 测试性能 ===");
        
        String testString = "电焊工操作危害因素噪声粉尘化学物质高温低温辐射振动";
        int iterations = 1000;
        
        // 测试拼音性能
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < iterations; i++) {
            PinyinUtil.getFirstChars(testString);
            PinyinUtil.getFullPinyin(testString);
        }
        long pinyinTime = System.currentTimeMillis() - startTime;
        
        // 测试五笔性能
        startTime = System.currentTimeMillis();
        for (int i = 0; i < iterations; i++) {
            WubiUtil.getWubiCode(testString);
            WubiUtil.getFullWubiCode(testString);
        }
        long wubiTime = System.currentTimeMillis() - startTime;
        
        log.info("性能测试结果 ({} 次迭代):", iterations);
        log.info("拼音处理耗时: {} ms", pinyinTime);
        log.info("五笔处理耗时: {} ms", wubiTime);
        log.info("平均每次拼音处理: {} ms", (double) pinyinTime / iterations);
        log.info("平均每次五笔处理: {} ms", (double) wubiTime / iterations);
    }
}
